import { PropType } from 'vue';
import { Color, HSV } from './interface';
declare const _default: import("vue").DefineComponent<{
    color: {
        type: PropType<Color>;
        required: true;
    };
    alpha: {
        type: NumberConstructor;
        required: true;
    };
    disabled: BooleanConstructor;
    disabledAlpha: BooleanConstructor;
    onHsvChange: PropType<(value: HSV) => void>;
    onAlphaChange: PropType<(value: number) => void>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    color?: unknown;
    alpha?: unknown;
    disabled?: unknown;
    disabledAlpha?: unknown;
    onHsvChange?: unknown;
    onAlphaChange?: unknown;
} & {
    disabled: boolean;
    color: Color;
    alpha: number;
    disabledAlpha: boolean;
} & {
    onAlphaChange?: ((value: number) => void) | undefined;
    onHsvChange?: ((value: HSV) => void) | undefined;
}>, {
    disabled: boolean;
    disabledAlpha: boolean;
}>;
export default _default;
