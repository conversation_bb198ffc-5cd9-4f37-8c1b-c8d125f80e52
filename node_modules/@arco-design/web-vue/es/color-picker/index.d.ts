import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _ColorPicker from './color-picker';
declare const ColorPicker: {
    new (...args: any[]): {
        $: import("vue").ComponentInternalInstance;
        $data: {};
        $props: Partial<{
            disabled: boolean;
            size: "mini" | "medium" | "large" | "small";
            hideTrigger: boolean;
            showText: boolean;
            showHistory: boolean;
            showPreset: boolean;
            disabledAlpha: boolean;
            presetColors: string[];
        }> & Omit<Readonly<{
            modelValue?: unknown;
            defaultValue?: unknown;
            format?: unknown;
            size?: unknown;
            showText?: unknown;
            showHistory?: unknown;
            showPreset?: unknown;
            disabled?: unknown;
            disabledAlpha?: unknown;
            hideTrigger?: unknown;
            triggerProps?: unknown;
            historyColors?: unknown;
            presetColors?: unknown;
        } & {
            disabled: boolean;
            size: "mini" | "medium" | "large" | "small";
            hideTrigger: boolean;
            showText: boolean;
            showHistory: boolean;
            showPreset: boolean;
            disabledAlpha: boolean;
            presetColors: string[];
        } & {
            format?: "hex" | "rgb" | undefined;
            defaultValue?: string | undefined;
            modelValue?: string | undefined;
            triggerProps?: Partial<import("..").TriggerProps> | undefined;
            historyColors?: string[] | undefined;
        }> & {
            onChange?: ((value: string) => any) | undefined;
            "onUpdate:modelValue"?: ((value: string) => any) | undefined;
            "onPopup-visible-change"?: ((visible: boolean, value: string) => any) | undefined;
        } & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, "disabled" | "size" | "hideTrigger" | "showText" | "showHistory" | "showPreset" | "disabledAlpha" | "presetColors">;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        };
        $slots: Readonly<{
            [name: string]: import("vue").Slot | undefined;
        }>;
        $root: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $parent: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $emit: ((event: "change", value: string) => void) & ((event: "update:modelValue", value: string) => void) & ((event: "popup-visible-change", visible: boolean, value: string) => void);
        $el: any;
        $options: import("vue").ComponentOptionsBase<Readonly<{
            modelValue?: unknown;
            defaultValue?: unknown;
            format?: unknown;
            size?: unknown;
            showText?: unknown;
            showHistory?: unknown;
            showPreset?: unknown;
            disabled?: unknown;
            disabledAlpha?: unknown;
            hideTrigger?: unknown;
            triggerProps?: unknown;
            historyColors?: unknown;
            presetColors?: unknown;
        } & {
            disabled: boolean;
            size: "mini" | "medium" | "large" | "small";
            hideTrigger: boolean;
            showText: boolean;
            showHistory: boolean;
            showPreset: boolean;
            disabledAlpha: boolean;
            presetColors: string[];
        } & {
            format?: "hex" | "rgb" | undefined;
            defaultValue?: string | undefined;
            modelValue?: string | undefined;
            triggerProps?: Partial<import("..").TriggerProps> | undefined;
            historyColors?: string[] | undefined;
        }> & {
            onChange?: ((value: string) => any) | undefined;
            "onUpdate:modelValue"?: ((value: string) => any) | undefined;
            "onPopup-visible-change"?: ((visible: boolean, value: string) => any) | undefined;
        }, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            'update:modelValue': (value: string) => true;
            change: (value: string) => true;
            'popup-visible-change': (visible: boolean, value: string) => true;
        }, string, {
            disabled: boolean;
            size: "mini" | "medium" | "large" | "small";
            hideTrigger: boolean;
            showText: boolean;
            showHistory: boolean;
            showPreset: boolean;
            disabledAlpha: boolean;
            presetColors: string[];
        }> & {
            beforeCreate?: ((() => void) | (() => void)[]) | undefined;
            created?: ((() => void) | (() => void)[]) | undefined;
            beforeMount?: ((() => void) | (() => void)[]) | undefined;
            mounted?: ((() => void) | (() => void)[]) | undefined;
            beforeUpdate?: ((() => void) | (() => void)[]) | undefined;
            updated?: ((() => void) | (() => void)[]) | undefined;
            activated?: ((() => void) | (() => void)[]) | undefined;
            deactivated?: ((() => void) | (() => void)[]) | undefined;
            beforeDestroy?: ((() => void) | (() => void)[]) | undefined;
            beforeUnmount?: ((() => void) | (() => void)[]) | undefined;
            destroyed?: ((() => void) | (() => void)[]) | undefined;
            unmounted?: ((() => void) | (() => void)[]) | undefined;
            renderTracked?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            renderTriggered?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            errorCaptured?: (((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void) | ((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void)[]) | undefined;
        };
        $forceUpdate: () => void;
        $nextTick: typeof import("vue").nextTick;
        $watch(source: string | Function, cb: Function, options?: import("vue").WatchOptions<boolean> | undefined): import("vue").WatchStopHandle;
    } & Readonly<{
        modelValue?: unknown;
        defaultValue?: unknown;
        format?: unknown;
        size?: unknown;
        showText?: unknown;
        showHistory?: unknown;
        showPreset?: unknown;
        disabled?: unknown;
        disabledAlpha?: unknown;
        hideTrigger?: unknown;
        triggerProps?: unknown;
        historyColors?: unknown;
        presetColors?: unknown;
    } & {
        disabled: boolean;
        size: "mini" | "medium" | "large" | "small";
        hideTrigger: boolean;
        showText: boolean;
        showHistory: boolean;
        showPreset: boolean;
        disabledAlpha: boolean;
        presetColors: string[];
    } & {
        format?: "hex" | "rgb" | undefined;
        defaultValue?: string | undefined;
        modelValue?: string | undefined;
        triggerProps?: Partial<import("..").TriggerProps> | undefined;
        historyColors?: string[] | undefined;
    }> & {
        onChange?: ((value: string) => any) | undefined;
        "onUpdate:modelValue"?: ((value: string) => any) | undefined;
        "onPopup-visible-change"?: ((visible: boolean, value: string) => any) | undefined;
    } & import("vue").ShallowUnwrapRef<() => JSX.Element> & {} & {} & import("vue").ComponentCustomProperties;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<{
    modelValue?: unknown;
    defaultValue?: unknown;
    format?: unknown;
    size?: unknown;
    showText?: unknown;
    showHistory?: unknown;
    showPreset?: unknown;
    disabled?: unknown;
    disabledAlpha?: unknown;
    hideTrigger?: unknown;
    triggerProps?: unknown;
    historyColors?: unknown;
    presetColors?: unknown;
} & {
    disabled: boolean;
    size: "mini" | "medium" | "large" | "small";
    hideTrigger: boolean;
    showText: boolean;
    showHistory: boolean;
    showPreset: boolean;
    disabledAlpha: boolean;
    presetColors: string[];
} & {
    format?: "hex" | "rgb" | undefined;
    defaultValue?: string | undefined;
    modelValue?: string | undefined;
    triggerProps?: Partial<import("..").TriggerProps> | undefined;
    historyColors?: string[] | undefined;
}> & {
    onChange?: ((value: string) => any) | undefined;
    "onUpdate:modelValue"?: ((value: string) => any) | undefined;
    "onPopup-visible-change"?: ((visible: boolean, value: string) => any) | undefined;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:modelValue': (value: string) => true;
    change: (value: string) => true;
    'popup-visible-change': (visible: boolean, value: string) => true;
}, string, {
    disabled: boolean;
    size: "mini" | "medium" | "large" | "small";
    hideTrigger: boolean;
    showText: boolean;
    showHistory: boolean;
    showPreset: boolean;
    disabledAlpha: boolean;
    presetColors: string[];
}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export type { RGB, HSV, Color } from './interface';
export declare type ColorPickerInstance = InstanceType<typeof _ColorPicker>;
export default ColorPicker;
