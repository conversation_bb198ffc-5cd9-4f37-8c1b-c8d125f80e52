import { PropType } from 'vue';
import { Color } from './interface';
declare const _default: import("vue").DefineComponent<{
    x: {
        type: NumberConstructor;
        required: true;
    };
    color: {
        type: PropType<Color>;
        required: true;
    };
    colorString: StringConstructor;
    type: PropType<"hue" | "alpha">;
    onChange: PropType<(x: number) => void>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    x?: unknown;
    color?: unknown;
    colorString?: unknown;
    type?: unknown;
    onChange?: unknown;
} & {
    color: Color;
    x: number;
} & {
    onChange?: ((x: number) => void) | undefined;
    type?: "hue" | "alpha" | undefined;
    colorString?: string | undefined;
}>, {}>;
export default _default;
