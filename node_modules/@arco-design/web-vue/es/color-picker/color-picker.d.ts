import { PropType } from 'vue';
import { TriggerProps } from '../trigger';
declare const _default: import("vue").DefineComponent<{
    modelValue: StringConstructor;
    defaultValue: {
        type: StringConstructor;
    };
    format: {
        type: PropType<"hex" | "rgb">;
    };
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
        default: string;
    };
    showText: {
        type: BooleanConstructor;
        default: boolean;
    };
    showHistory: {
        type: BooleanConstructor;
        default: boolean;
    };
    showPreset: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabledAlpha: {
        type: BooleanConstructor;
        default: boolean;
    };
    hideTrigger: {
        type: BooleanConstructor;
    };
    triggerProps: {
        type: PropType<Partial<TriggerProps>>;
    };
    historyColors: {
        type: PropType<string[]>;
    };
    presetColors: {
        type: PropType<string[]>;
        default: () => string[];
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:modelValue': (value: string) => true;
    change: (value: string) => true;
    'popup-visible-change': (visible: boolean, value: string) => true;
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    modelValue?: unknown;
    defaultValue?: unknown;
    format?: unknown;
    size?: unknown;
    showText?: unknown;
    showHistory?: unknown;
    showPreset?: unknown;
    disabled?: unknown;
    disabledAlpha?: unknown;
    hideTrigger?: unknown;
    triggerProps?: unknown;
    historyColors?: unknown;
    presetColors?: unknown;
} & {
    disabled: boolean;
    size: "mini" | "medium" | "large" | "small";
    hideTrigger: boolean;
    showText: boolean;
    showHistory: boolean;
    showPreset: boolean;
    disabledAlpha: boolean;
    presetColors: string[];
} & {
    format?: "hex" | "rgb" | undefined;
    defaultValue?: string | undefined;
    modelValue?: string | undefined;
    triggerProps?: Partial<TriggerProps> | undefined;
    historyColors?: string[] | undefined;
}> & {
    onChange?: ((value: string) => any) | undefined;
    "onUpdate:modelValue"?: ((value: string) => any) | undefined;
    "onPopup-visible-change"?: ((visible: boolean, value: string) => any) | undefined;
}, {
    disabled: boolean;
    size: "mini" | "medium" | "large" | "small";
    hideTrigger: boolean;
    showText: boolean;
    showHistory: boolean;
    showPreset: boolean;
    disabledAlpha: boolean;
    presetColors: string[];
}>;
export default _default;
