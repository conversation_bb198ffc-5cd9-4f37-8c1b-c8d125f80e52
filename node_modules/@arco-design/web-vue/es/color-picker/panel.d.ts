import { PropType } from 'vue';
import { Color, HSV } from './interface';
declare const _default: import("vue").DefineComponent<{
    color: {
        type: PropType<Color>;
        required: true;
    };
    alpha: {
        type: NumberConstructor;
        required: true;
    };
    colorString: StringConstructor;
    disabled: BooleanConstructor;
    disabledAlpha: BooleanConstructor;
    showHistory: BooleanConstructor;
    showPreset: BooleanConstructor;
    format: PropType<"hex" | "rgb">;
    historyColors: PropType<string[]>;
    presetColors: PropType<string[]>;
    onAlphaChange: PropType<(alpha: number) => void>;
    onHsvChange: PropType<(hsv: HSV) => void>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    color?: unknown;
    alpha?: unknown;
    colorString?: unknown;
    disabled?: unknown;
    disabledAlpha?: unknown;
    showHistory?: unknown;
    showPreset?: unknown;
    format?: unknown;
    historyColors?: unknown;
    presetColors?: unknown;
    onAlphaChange?: unknown;
    onHsvChange?: unknown;
} & {
    disabled: boolean;
    color: Color;
    alpha: number;
    showHistory: boolean;
    showPreset: boolean;
    disabledAlpha: boolean;
} & {
    format?: "hex" | "rgb" | undefined;
    historyColors?: string[] | undefined;
    presetColors?: string[] | undefined;
    colorString?: string | undefined;
    onAlphaChange?: ((alpha: number) => void) | undefined;
    onHsvChange?: ((hsv: HSV) => void) | undefined;
}>, {
    disabled: boolean;
    showHistory: boolean;
    showPreset: boolean;
    disabledAlpha: boolean;
}>;
export default _default;
