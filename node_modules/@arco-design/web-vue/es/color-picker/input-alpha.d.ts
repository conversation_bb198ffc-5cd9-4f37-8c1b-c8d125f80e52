import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<{
    value: {
        type: NumberConstructor;
        required: true;
    };
    disabled: BooleanConstructor;
    onChange: PropType<(value: number) => void>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    value?: unknown;
    disabled?: unknown;
    onChange?: unknown;
} & {
    disabled: boolean;
    value: number;
} & {
    onChange?: ((value: number) => void) | undefined;
}>, {
    disabled: boolean;
}>;
export default _default;
