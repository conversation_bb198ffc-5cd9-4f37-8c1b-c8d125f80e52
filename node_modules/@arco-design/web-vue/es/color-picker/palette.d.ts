import { PropType } from 'vue';
import { Color } from './interface';
declare const _default: import("vue").DefineComponent<{
    color: {
        type: PropType<Color>;
        required: true;
    };
    onChange: PropType<(s: number, v: number) => void>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    color?: unknown;
    onChange?: unknown;
} & {
    color: Color;
} & {
    onChange?: ((s: number, v: number) => void) | undefined;
}>, {}>;
export default _default;
