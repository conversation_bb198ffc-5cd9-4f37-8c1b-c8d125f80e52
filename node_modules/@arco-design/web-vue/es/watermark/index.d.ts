import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _Watermark from './watermark';
declare const Watermark: {
    new (...args: any[]): {
        $: import("vue").ComponentInternalInstance;
        $data: {};
        $props: Partial<{
            repeat: boolean;
            alpha: number;
            gap: [number, number];
            rotate: number;
            zIndex: number;
            antiTamper: boolean;
            grayscale: boolean;
            staggered: boolean;
        }> & Omit<Readonly<{
            content?: unknown;
            image?: unknown;
            width?: unknown;
            height?: unknown;
            gap?: unknown;
            offset?: unknown;
            rotate?: unknown;
            font?: unknown;
            zIndex?: unknown;
            alpha?: unknown;
            antiTamper?: unknown;
            grayscale?: unknown;
            repeat?: unknown;
            staggered?: unknown;
        } & {
            repeat: boolean;
            alpha: number;
            gap: [number, number];
            rotate: number;
            zIndex: number;
            antiTamper: boolean;
            grayscale: boolean;
            staggered: boolean;
        } & {
            content?: string | string[] | undefined;
            image?: string | undefined;
            height?: number | undefined;
            font?: import("./interface").WatermarkFont | undefined;
            offset?: [number, number] | undefined;
            width?: number | undefined;
        }> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, "repeat" | "alpha" | "gap" | "rotate" | "zIndex" | "antiTamper" | "grayscale" | "staggered">;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        };
        $slots: Readonly<{
            [name: string]: import("vue").Slot | undefined;
        }>;
        $root: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $parent: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $emit: (event: string, ...args: any[]) => void;
        $el: any;
        $options: import("vue").ComponentOptionsBase<Readonly<{
            content?: unknown;
            image?: unknown;
            width?: unknown;
            height?: unknown;
            gap?: unknown;
            offset?: unknown;
            rotate?: unknown;
            font?: unknown;
            zIndex?: unknown;
            alpha?: unknown;
            antiTamper?: unknown;
            grayscale?: unknown;
            repeat?: unknown;
            staggered?: unknown;
        } & {
            repeat: boolean;
            alpha: number;
            gap: [number, number];
            rotate: number;
            zIndex: number;
            antiTamper: boolean;
            grayscale: boolean;
            staggered: boolean;
        } & {
            content?: string | string[] | undefined;
            image?: string | undefined;
            height?: number | undefined;
            font?: import("./interface").WatermarkFont | undefined;
            offset?: [number, number] | undefined;
            width?: number | undefined;
        }>, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
            repeat: boolean;
            alpha: number;
            gap: [number, number];
            rotate: number;
            zIndex: number;
            antiTamper: boolean;
            grayscale: boolean;
            staggered: boolean;
        }> & {
            beforeCreate?: ((() => void) | (() => void)[]) | undefined;
            created?: ((() => void) | (() => void)[]) | undefined;
            beforeMount?: ((() => void) | (() => void)[]) | undefined;
            mounted?: ((() => void) | (() => void)[]) | undefined;
            beforeUpdate?: ((() => void) | (() => void)[]) | undefined;
            updated?: ((() => void) | (() => void)[]) | undefined;
            activated?: ((() => void) | (() => void)[]) | undefined;
            deactivated?: ((() => void) | (() => void)[]) | undefined;
            beforeDestroy?: ((() => void) | (() => void)[]) | undefined;
            beforeUnmount?: ((() => void) | (() => void)[]) | undefined;
            destroyed?: ((() => void) | (() => void)[]) | undefined;
            unmounted?: ((() => void) | (() => void)[]) | undefined;
            renderTracked?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            renderTriggered?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            errorCaptured?: (((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void) | ((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void)[]) | undefined;
        };
        $forceUpdate: () => void;
        $nextTick: typeof import("vue").nextTick;
        $watch(source: string | Function, cb: Function, options?: import("vue").WatchOptions<boolean> | undefined): import("vue").WatchStopHandle;
    } & Readonly<{
        content?: unknown;
        image?: unknown;
        width?: unknown;
        height?: unknown;
        gap?: unknown;
        offset?: unknown;
        rotate?: unknown;
        font?: unknown;
        zIndex?: unknown;
        alpha?: unknown;
        antiTamper?: unknown;
        grayscale?: unknown;
        repeat?: unknown;
        staggered?: unknown;
    } & {
        repeat: boolean;
        alpha: number;
        gap: [number, number];
        rotate: number;
        zIndex: number;
        antiTamper: boolean;
        grayscale: boolean;
        staggered: boolean;
    } & {
        content?: string | string[] | undefined;
        image?: string | undefined;
        height?: number | undefined;
        font?: import("./interface").WatermarkFont | undefined;
        offset?: [number, number] | undefined;
        width?: number | undefined;
    }> & import("vue").ShallowUnwrapRef<() => JSX.Element> & {} & {} & import("vue").ComponentCustomProperties;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<{
    content?: unknown;
    image?: unknown;
    width?: unknown;
    height?: unknown;
    gap?: unknown;
    offset?: unknown;
    rotate?: unknown;
    font?: unknown;
    zIndex?: unknown;
    alpha?: unknown;
    antiTamper?: unknown;
    grayscale?: unknown;
    repeat?: unknown;
    staggered?: unknown;
} & {
    repeat: boolean;
    alpha: number;
    gap: [number, number];
    rotate: number;
    zIndex: number;
    antiTamper: boolean;
    grayscale: boolean;
    staggered: boolean;
} & {
    content?: string | string[] | undefined;
    image?: string | undefined;
    height?: number | undefined;
    font?: import("./interface").WatermarkFont | undefined;
    offset?: [number, number] | undefined;
    width?: number | undefined;
}>, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    repeat: boolean;
    alpha: number;
    gap: [number, number];
    rotate: number;
    zIndex: number;
    antiTamper: boolean;
    grayscale: boolean;
    staggered: boolean;
}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type WatermarkInstance = InstanceType<typeof _Watermark>;
export default Watermark;
