import { PropType } from 'vue';
import { WatermarkFont } from './interface';
declare const _default: import("vue").DefineComponent<{
    content: {
        type: PropType<string | string[]>;
    };
    image: {
        type: StringConstructor;
    };
    width: {
        type: NumberConstructor;
    };
    height: {
        type: NumberConstructor;
    };
    gap: {
        type: PropType<[number, number]>;
        default: () => number[];
    };
    offset: {
        type: PropType<[number, number]>;
    };
    rotate: {
        type: NumberConstructor;
        default: number;
    };
    font: {
        type: PropType<WatermarkFont>;
    };
    zIndex: {
        type: NumberConstructor;
        default: number;
    };
    alpha: {
        type: NumberConstructor;
        default: number;
    };
    antiTamper: {
        type: BooleanConstructor;
        default: boolean;
    };
    grayscale: {
        type: BooleanConstructor;
        default: boolean;
    };
    repeat: {
        type: BooleanConstructor;
        default: boolean;
    };
    staggered: {
        type: BooleanConstructor;
        default: boolean;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    content?: unknown;
    image?: unknown;
    width?: unknown;
    height?: unknown;
    gap?: unknown;
    offset?: unknown;
    rotate?: unknown;
    font?: unknown;
    zIndex?: unknown;
    alpha?: unknown;
    antiTamper?: unknown;
    grayscale?: unknown;
    repeat?: unknown;
    staggered?: unknown;
} & {
    repeat: boolean;
    alpha: number;
    gap: [number, number];
    rotate: number;
    zIndex: number;
    antiTamper: boolean;
    grayscale: boolean;
    staggered: boolean;
} & {
    content?: string | string[] | undefined;
    image?: string | undefined;
    height?: number | undefined;
    font?: WatermarkFont | undefined;
    offset?: [number, number] | undefined;
    width?: number | undefined;
}>, {
    repeat: boolean;
    alpha: number;
    gap: [number, number];
    rotate: number;
    zIndex: number;
    antiTamper: boolean;
    grayscale: boolean;
    staggered: boolean;
}>;
export default _default;
