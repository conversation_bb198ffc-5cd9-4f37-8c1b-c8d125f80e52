/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
.arco-popconfirm-popup-content {
  box-sizing: border-box;
  padding: 16px 16px;
  color: var(--color-text-2);
  font-size: 14px;
  line-height: 1.5715;
  background-color: var(--color-bg-popup);
  border: 1px solid var(--color-neutral-3);
  border-radius: var(--border-radius-medium);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
.arco-popconfirm-popup-content .arco-popconfirm-body {
  position: relative;
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  color: var(--color-text-1);
  font-size: 14px;
}
.arco-popconfirm-popup-content .arco-popconfirm-body .arco-popconfirm-icon {
  display: inline-flex;
  align-items: center;
  height: 22.001px;
  margin-right: 8px;
  font-size: 18px;
}
.arco-popconfirm-popup-content .arco-popconfirm-body .arco-popconfirm-icon .arco-icon-exclamation-circle-fill {
  color: rgb(var(--warning-6));
}
.arco-popconfirm-popup-content .arco-popconfirm-body .arco-popconfirm-icon .arco-icon-check-circle-fill {
  color: rgb(var(--success-6));
}
.arco-popconfirm-popup-content .arco-popconfirm-body .arco-popconfirm-icon .arco-icon-info-circle-fill {
  color: rgb(var(--primary-6));
}
.arco-popconfirm-popup-content .arco-popconfirm-body .arco-popconfirm-icon .arco-icon-close-circle-fill {
  color: rgb(var(--danger-6));
}
.arco-popconfirm-popup-content .arco-popconfirm-body .arco-popconfirm-content {
  text-align: left;
  word-wrap: break-word;
}
.arco-popconfirm-popup-content .arco-popconfirm-footer {
  text-align: right;
}
.arco-popconfirm-popup-content .arco-popconfirm-footer > button {
  margin-left: 8px;
}
.arco-popconfirm-popup-arrow {
  z-index: 1;
  background-color: var(--color-bg-popup);
  border: 1px solid var(--color-neutral-3);
}
