@import '../../style/theme/index.less';

@popconfirm-padding-horizontal: @spacing-7;
@popconfirm-padding-vertical: @spacing-7;
@popconfirm-margin-title-bottom: @spacing-7;
@popconfirm-size-title-icon: 18px;
@popconfirm-margin-icon-right: @spacing-4;
@popconfirm-margin-button-left: @spacing-4;
@popconfirm-font-title-size: @font-size-body-3;
@popconfirm-color-title-text: var(~'@{arco-cssvars-prefix}-color-text-1');

@popconfirm-color-content-bg: var(~'@{arco-cssvars-prefix}-color-bg-popup');
@popconfirm-color-border: var(~'@{arco-cssvars-prefix}-color-neutral-3');
@popconfirm-border-radius: @radius-medium;
@popconfirm-shadow: @shadow2-down;
// type
@popconfirm-color-title-icon: @color-warning-6;
@popconfirm-color-title-icon_danger: @color-danger-6;
@popconfirm-color-title-icon_success: @color-success-6;
@popconfirm-color-title-icon_info: @color-primary-6;
