import type { PropType } from 'vue';
import { CSSProperties } from 'vue';
import { ButtonProps } from '../button';
import { ClassName } from '../_utils/types';
declare const _default: import("vue").DefineComponent<{
    content: StringConstructor;
    position: {
        type: PropType<"top" | "tl" | "tr" | "bottom" | "bl" | "br" | "left" | "lt" | "lb" | "right" | "rt" | "rb">;
        default: string;
    };
    popupVisible: {
        type: BooleanConstructor;
        default: undefined;
    };
    defaultPopupVisible: {
        type: BooleanConstructor;
        default: boolean;
    };
    type: {
        type: PropType<"error" | "success" | "warning" | "info">;
        default: string;
    };
    okText: StringConstructor;
    cancelText: StringConstructor;
    okLoading: {
        type: BooleanConstructor;
        default: boolean;
    };
    okButtonProps: {
        type: PropType<ButtonProps>;
    };
    cancelButtonProps: {
        type: PropType<ButtonProps>;
    };
    contentClass: {
        type: PropType<ClassName>;
    };
    contentStyle: {
        type: PropType<CSSProperties>;
    };
    arrowClass: {
        type: PropType<ClassName>;
    };
    arrowStyle: {
        type: PropType<CSSProperties>;
    };
    popupContainer: {
        type: PropType<string | HTMLElement>;
    };
    onBeforeOk: {
        type: PropType<(done: (closed: boolean) => void) => void | boolean | Promise<void | boolean>>;
    };
    onBeforeCancel: {
        type: PropType<() => boolean>;
    };
}, {
    prefixCls: string;
    contentCls: import("vue").ComputedRef<(ClassName | undefined)[]>;
    arrowCls: import("vue").ComputedRef<(ClassName | undefined)[]>;
    computedPopupVisible: import("vue").ComputedRef<boolean>;
    mergedOkLoading: import("vue").ComputedRef<boolean>;
    handlePopupVisibleChange: (visible: boolean) => void;
    handleOk: () => Promise<void>;
    handleCancel: () => void;
    t: (key: string, ...args: any[]) => string;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:popupVisible': (visible: boolean) => true;
    popupVisibleChange: (visible: boolean) => true;
    ok: () => true;
    cancel: () => true;
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    content?: unknown;
    position?: unknown;
    popupVisible?: unknown;
    defaultPopupVisible?: unknown;
    type?: unknown;
    okText?: unknown;
    cancelText?: unknown;
    okLoading?: unknown;
    okButtonProps?: unknown;
    cancelButtonProps?: unknown;
    contentClass?: unknown;
    contentStyle?: unknown;
    arrowClass?: unknown;
    arrowStyle?: unknown;
    popupContainer?: unknown;
    onBeforeOk?: unknown;
    onBeforeCancel?: unknown;
} & {
    defaultPopupVisible: boolean;
    position: "top" | "tl" | "tr" | "bottom" | "bl" | "br" | "left" | "lt" | "lb" | "right" | "rt" | "rb";
    type: "error" | "success" | "warning" | "info";
    okLoading: boolean;
} & {
    popupVisible?: boolean | undefined;
    contentClass?: ClassName | undefined;
    contentStyle?: CSSProperties | undefined;
    arrowClass?: ClassName | undefined;
    arrowStyle?: CSSProperties | undefined;
    popupContainer?: string | HTMLElement | undefined;
    content?: string | undefined;
    okText?: string | undefined;
    cancelText?: string | undefined;
    okButtonProps?: ButtonProps | undefined;
    cancelButtonProps?: ButtonProps | undefined;
    onBeforeOk?: ((done: (closed: boolean) => void) => void | boolean | Promise<void | boolean>) | undefined;
    onBeforeCancel?: (() => boolean) | undefined;
}> & {
    "onUpdate:popupVisible"?: ((visible: boolean) => any) | undefined;
    onPopupVisibleChange?: ((visible: boolean) => any) | undefined;
    onOk?: (() => any) | undefined;
    onCancel?: (() => any) | undefined;
}, {
    popupVisible: boolean;
    defaultPopupVisible: boolean;
    position: "top" | "tl" | "tr" | "bottom" | "bl" | "br" | "left" | "lt" | "lb" | "right" | "rt" | "rb";
    type: "error" | "success" | "warning" | "info";
    okLoading: boolean;
}>;
export default _default;
