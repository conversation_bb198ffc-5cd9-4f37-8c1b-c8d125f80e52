import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _TimePicker from './time-picker';
declare const TimePicker: {
    new (...args: any[]): {
        $: import("vue").ComponentInternalInstance;
        $data: {};
        $props: Partial<{
            popupVisible: boolean;
            defaultPopupVisible: boolean;
            position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
            disabled: boolean;
            unmountOnClose: boolean;
            type: "time" | "time-range";
            readonly: boolean;
            error: boolean;
            allowClear: boolean;
            format: string;
            disableConfirm: boolean;
            use12Hours: boolean;
            hideDisabledOptions: boolean;
        }> & Omit<Readonly<{
            type?: unknown;
            modelValue?: unknown;
            defaultValue?: unknown;
            disabled?: unknown;
            allowClear?: unknown;
            readonly?: unknown;
            error?: unknown;
            format?: unknown;
            placeholder?: unknown;
            size?: unknown;
            popupContainer?: unknown;
            use12Hours?: unknown;
            step?: unknown;
            disabledHours?: unknown;
            disabledMinutes?: unknown;
            disabledSeconds?: unknown;
            hideDisabledOptions?: unknown;
            disableConfirm?: unknown;
            position?: unknown;
            popupVisible?: unknown;
            defaultPopupVisible?: unknown;
            triggerProps?: unknown;
            unmountOnClose?: unknown;
        } & {
            defaultPopupVisible: boolean;
            position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
            disabled: boolean;
            unmountOnClose: boolean;
            type: "time" | "time-range";
            readonly: boolean;
            error: boolean;
            allowClear: boolean;
            format: string;
            disableConfirm: boolean;
            use12Hours: boolean;
            hideDisabledOptions: boolean;
        } & {
            popupVisible?: boolean | undefined;
            popupContainer?: string | HTMLElement | undefined;
            size?: "mini" | "medium" | "large" | "small" | undefined;
            placeholder?: string | string[] | undefined;
            defaultValue?: string | number | Date | (string | number | Date)[] | undefined;
            modelValue?: string | number | Date | (string | number | Date)[] | undefined;
            triggerProps?: import("..").TriggerProps | undefined;
            step?: {
                hour?: number | undefined;
                minute?: number | undefined;
                second?: number | undefined;
            } | undefined;
            disabledHours?: (() => number[]) | undefined;
            disabledMinutes?: ((selectedHour?: number | undefined) => number[]) | undefined;
            disabledSeconds?: ((selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]) | undefined;
        }> & {
            "onUpdate:popupVisible"?: ((visible: boolean) => any) | undefined;
            onChange?: ((timeString: string | (string | undefined)[] | undefined, time: Date | (Date | undefined)[] | undefined) => any) | undefined;
            onSelect?: ((timeString: string | (string | undefined)[], time: Date | (Date | undefined)[]) => any) | undefined;
            onClear?: (() => any) | undefined;
            "onUpdate:modelValue"?: ((timeString: string | (string | undefined)[] | undefined) => any) | undefined;
            "onPopup-visible-change"?: ((visible: boolean) => any) | undefined;
        } & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, "popupVisible" | "defaultPopupVisible" | "position" | "disabled" | "unmountOnClose" | "type" | "readonly" | "error" | "allowClear" | "format" | "disableConfirm" | "use12Hours" | "hideDisabledOptions">;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        };
        $slots: Readonly<{
            [name: string]: import("vue").Slot | undefined;
        }>;
        $root: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $parent: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $emit: ((event: "update:popupVisible", visible: boolean) => void) & ((event: "clear") => void) & ((event: "select", timeString: string | (string | undefined)[], time: Date | (Date | undefined)[]) => void) & ((event: "change", timeString: string | (string | undefined)[] | undefined, time: Date | (Date | undefined)[] | undefined) => void) & ((event: "update:modelValue", timeString: string | (string | undefined)[] | undefined) => void) & ((event: "popup-visible-change", visible: boolean) => void);
        $el: any;
        $options: import("vue").ComponentOptionsBase<Readonly<{
            type?: unknown;
            modelValue?: unknown;
            defaultValue?: unknown;
            disabled?: unknown;
            allowClear?: unknown;
            readonly?: unknown;
            error?: unknown;
            format?: unknown;
            placeholder?: unknown;
            size?: unknown;
            popupContainer?: unknown;
            use12Hours?: unknown;
            step?: unknown;
            disabledHours?: unknown;
            disabledMinutes?: unknown;
            disabledSeconds?: unknown;
            hideDisabledOptions?: unknown;
            disableConfirm?: unknown;
            position?: unknown;
            popupVisible?: unknown;
            defaultPopupVisible?: unknown;
            triggerProps?: unknown;
            unmountOnClose?: unknown;
        } & {
            defaultPopupVisible: boolean;
            position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
            disabled: boolean;
            unmountOnClose: boolean;
            type: "time" | "time-range";
            readonly: boolean;
            error: boolean;
            allowClear: boolean;
            format: string;
            disableConfirm: boolean;
            use12Hours: boolean;
            hideDisabledOptions: boolean;
        } & {
            popupVisible?: boolean | undefined;
            popupContainer?: string | HTMLElement | undefined;
            size?: "mini" | "medium" | "large" | "small" | undefined;
            placeholder?: string | string[] | undefined;
            defaultValue?: string | number | Date | (string | number | Date)[] | undefined;
            modelValue?: string | number | Date | (string | number | Date)[] | undefined;
            triggerProps?: import("..").TriggerProps | undefined;
            step?: {
                hour?: number | undefined;
                minute?: number | undefined;
                second?: number | undefined;
            } | undefined;
            disabledHours?: (() => number[]) | undefined;
            disabledMinutes?: ((selectedHour?: number | undefined) => number[]) | undefined;
            disabledSeconds?: ((selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]) | undefined;
        }> & {
            "onUpdate:popupVisible"?: ((visible: boolean) => any) | undefined;
            onChange?: ((timeString: string | (string | undefined)[] | undefined, time: Date | (Date | undefined)[] | undefined) => any) | undefined;
            onSelect?: ((timeString: string | (string | undefined)[], time: Date | (Date | undefined)[]) => any) | undefined;
            onClear?: (() => any) | undefined;
            "onUpdate:modelValue"?: ((timeString: string | (string | undefined)[] | undefined) => any) | undefined;
            "onPopup-visible-change"?: ((visible: boolean) => any) | undefined;
        }, {
            refInput: import("vue").Ref<any>;
            isRange: import("vue").ComputedRef<boolean>;
            prefixCls: string;
            panelVisible: import("vue").ComputedRef<boolean>;
            focusedInputIndex: import("vue").Ref<number>;
            computedPlaceholder: import("vue").ComputedRef<string | string[]>;
            panelValue: import("vue").Ref<import("dayjs").Dayjs | (import("dayjs").Dayjs | undefined)[] | undefined>;
            inputValue: import("vue").Ref<string | (string | undefined)[] | undefined>;
            computedFormat: import("vue").ComputedRef<string>;
            computedUse12Hours: import("vue").ComputedRef<boolean>;
            inputProps: import("vue").ComputedRef<{
                focusedIndex: number;
                onFocusedIndexChange: (index: number) => void;
                onChange: (e: any) => void;
                onPressEnter: () => void;
            } | {
                onChange: (e: any) => void;
                onPressEnter: () => void;
                focusedIndex?: undefined;
                onFocusedIndexChange?: undefined;
            }>;
            panelProps: import("vue").ComputedRef<{
                displayIndex: number;
                onDisplayIndexChange: (index: number) => void;
            } | {
                displayIndex?: undefined;
                onDisplayIndexChange?: undefined;
            }>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            onPanelVisibleChange: any;
            onInputClear: (e: Event) => void;
            onPanelSelect: (value: import("dayjs").Dayjs | (import("dayjs").Dayjs | undefined)[]) => void;
            onPanelConfirm: (value: import("dayjs").Dayjs | import("dayjs").Dayjs[]) => void;
            onPanelClick: () => void;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            change: (timeString: string | (string | undefined)[] | undefined, time: Date | (Date | undefined)[] | undefined) => true;
            'update:modelValue': (timeString: string | (string | undefined)[] | undefined) => true;
            select: (timeString: string | (string | undefined)[], time: Date | (Date | undefined)[]) => true;
            clear: () => true;
            'popup-visible-change': (visible: boolean) => true;
            'update:popupVisible': (visible: boolean) => true;
        }, string, {
            popupVisible: boolean;
            defaultPopupVisible: boolean;
            position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
            disabled: boolean;
            unmountOnClose: boolean;
            type: "time" | "time-range";
            readonly: boolean;
            error: boolean;
            allowClear: boolean;
            format: string;
            disableConfirm: boolean;
            use12Hours: boolean;
            hideDisabledOptions: boolean;
        }> & {
            beforeCreate?: ((() => void) | (() => void)[]) | undefined;
            created?: ((() => void) | (() => void)[]) | undefined;
            beforeMount?: ((() => void) | (() => void)[]) | undefined;
            mounted?: ((() => void) | (() => void)[]) | undefined;
            beforeUpdate?: ((() => void) | (() => void)[]) | undefined;
            updated?: ((() => void) | (() => void)[]) | undefined;
            activated?: ((() => void) | (() => void)[]) | undefined;
            deactivated?: ((() => void) | (() => void)[]) | undefined;
            beforeDestroy?: ((() => void) | (() => void)[]) | undefined;
            beforeUnmount?: ((() => void) | (() => void)[]) | undefined;
            destroyed?: ((() => void) | (() => void)[]) | undefined;
            unmounted?: ((() => void) | (() => void)[]) | undefined;
            renderTracked?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            renderTriggered?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            errorCaptured?: (((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void) | ((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void)[]) | undefined;
        };
        $forceUpdate: () => void;
        $nextTick: typeof import("vue").nextTick;
        $watch(source: string | Function, cb: Function, options?: import("vue").WatchOptions<boolean> | undefined): import("vue").WatchStopHandle;
    } & Readonly<{
        type?: unknown;
        modelValue?: unknown;
        defaultValue?: unknown;
        disabled?: unknown;
        allowClear?: unknown;
        readonly?: unknown;
        error?: unknown;
        format?: unknown;
        placeholder?: unknown;
        size?: unknown;
        popupContainer?: unknown;
        use12Hours?: unknown;
        step?: unknown;
        disabledHours?: unknown;
        disabledMinutes?: unknown;
        disabledSeconds?: unknown;
        hideDisabledOptions?: unknown;
        disableConfirm?: unknown;
        position?: unknown;
        popupVisible?: unknown;
        defaultPopupVisible?: unknown;
        triggerProps?: unknown;
        unmountOnClose?: unknown;
    } & {
        defaultPopupVisible: boolean;
        position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
        disabled: boolean;
        unmountOnClose: boolean;
        type: "time" | "time-range";
        readonly: boolean;
        error: boolean;
        allowClear: boolean;
        format: string;
        disableConfirm: boolean;
        use12Hours: boolean;
        hideDisabledOptions: boolean;
    } & {
        popupVisible?: boolean | undefined;
        popupContainer?: string | HTMLElement | undefined;
        size?: "mini" | "medium" | "large" | "small" | undefined;
        placeholder?: string | string[] | undefined;
        defaultValue?: string | number | Date | (string | number | Date)[] | undefined;
        modelValue?: string | number | Date | (string | number | Date)[] | undefined;
        triggerProps?: import("..").TriggerProps | undefined;
        step?: {
            hour?: number | undefined;
            minute?: number | undefined;
            second?: number | undefined;
        } | undefined;
        disabledHours?: (() => number[]) | undefined;
        disabledMinutes?: ((selectedHour?: number | undefined) => number[]) | undefined;
        disabledSeconds?: ((selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]) | undefined;
    }> & {
        "onUpdate:popupVisible"?: ((visible: boolean) => any) | undefined;
        onChange?: ((timeString: string | (string | undefined)[] | undefined, time: Date | (Date | undefined)[] | undefined) => any) | undefined;
        onSelect?: ((timeString: string | (string | undefined)[], time: Date | (Date | undefined)[]) => any) | undefined;
        onClear?: (() => any) | undefined;
        "onUpdate:modelValue"?: ((timeString: string | (string | undefined)[] | undefined) => any) | undefined;
        "onPopup-visible-change"?: ((visible: boolean) => any) | undefined;
    } & import("vue").ShallowUnwrapRef<{
        refInput: import("vue").Ref<any>;
        isRange: import("vue").ComputedRef<boolean>;
        prefixCls: string;
        panelVisible: import("vue").ComputedRef<boolean>;
        focusedInputIndex: import("vue").Ref<number>;
        computedPlaceholder: import("vue").ComputedRef<string | string[]>;
        panelValue: import("vue").Ref<import("dayjs").Dayjs | (import("dayjs").Dayjs | undefined)[] | undefined>;
        inputValue: import("vue").Ref<string | (string | undefined)[] | undefined>;
        computedFormat: import("vue").ComputedRef<string>;
        computedUse12Hours: import("vue").ComputedRef<boolean>;
        inputProps: import("vue").ComputedRef<{
            focusedIndex: number;
            onFocusedIndexChange: (index: number) => void;
            onChange: (e: any) => void;
            onPressEnter: () => void;
        } | {
            onChange: (e: any) => void;
            onPressEnter: () => void;
            focusedIndex?: undefined;
            onFocusedIndexChange?: undefined;
        }>;
        panelProps: import("vue").ComputedRef<{
            displayIndex: number;
            onDisplayIndexChange: (index: number) => void;
        } | {
            displayIndex?: undefined;
            onDisplayIndexChange?: undefined;
        }>;
        mergedDisabled: import("vue").ComputedRef<boolean>;
        onPanelVisibleChange: any;
        onInputClear: (e: Event) => void;
        onPanelSelect: (value: import("dayjs").Dayjs | (import("dayjs").Dayjs | undefined)[]) => void;
        onPanelConfirm: (value: import("dayjs").Dayjs | import("dayjs").Dayjs[]) => void;
        onPanelClick: () => void;
    }> & {} & {} & import("vue").ComponentCustomProperties;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<{
    type?: unknown;
    modelValue?: unknown;
    defaultValue?: unknown;
    disabled?: unknown;
    allowClear?: unknown;
    readonly?: unknown;
    error?: unknown;
    format?: unknown;
    placeholder?: unknown;
    size?: unknown;
    popupContainer?: unknown;
    use12Hours?: unknown;
    step?: unknown;
    disabledHours?: unknown;
    disabledMinutes?: unknown;
    disabledSeconds?: unknown;
    hideDisabledOptions?: unknown;
    disableConfirm?: unknown;
    position?: unknown;
    popupVisible?: unknown;
    defaultPopupVisible?: unknown;
    triggerProps?: unknown;
    unmountOnClose?: unknown;
} & {
    defaultPopupVisible: boolean;
    position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
    disabled: boolean;
    unmountOnClose: boolean;
    type: "time" | "time-range";
    readonly: boolean;
    error: boolean;
    allowClear: boolean;
    format: string;
    disableConfirm: boolean;
    use12Hours: boolean;
    hideDisabledOptions: boolean;
} & {
    popupVisible?: boolean | undefined;
    popupContainer?: string | HTMLElement | undefined;
    size?: "mini" | "medium" | "large" | "small" | undefined;
    placeholder?: string | string[] | undefined;
    defaultValue?: string | number | Date | (string | number | Date)[] | undefined;
    modelValue?: string | number | Date | (string | number | Date)[] | undefined;
    triggerProps?: import("..").TriggerProps | undefined;
    step?: {
        hour?: number | undefined;
        minute?: number | undefined;
        second?: number | undefined;
    } | undefined;
    disabledHours?: (() => number[]) | undefined;
    disabledMinutes?: ((selectedHour?: number | undefined) => number[]) | undefined;
    disabledSeconds?: ((selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]) | undefined;
}> & {
    "onUpdate:popupVisible"?: ((visible: boolean) => any) | undefined;
    onChange?: ((timeString: string | (string | undefined)[] | undefined, time: Date | (Date | undefined)[] | undefined) => any) | undefined;
    onSelect?: ((timeString: string | (string | undefined)[], time: Date | (Date | undefined)[]) => any) | undefined;
    onClear?: (() => any) | undefined;
    "onUpdate:modelValue"?: ((timeString: string | (string | undefined)[] | undefined) => any) | undefined;
    "onPopup-visible-change"?: ((visible: boolean) => any) | undefined;
}, {
    refInput: import("vue").Ref<any>;
    isRange: import("vue").ComputedRef<boolean>;
    prefixCls: string;
    panelVisible: import("vue").ComputedRef<boolean>;
    focusedInputIndex: import("vue").Ref<number>;
    computedPlaceholder: import("vue").ComputedRef<string | string[]>;
    panelValue: import("vue").Ref<import("dayjs").Dayjs | (import("dayjs").Dayjs | undefined)[] | undefined>;
    inputValue: import("vue").Ref<string | (string | undefined)[] | undefined>;
    computedFormat: import("vue").ComputedRef<string>;
    computedUse12Hours: import("vue").ComputedRef<boolean>;
    inputProps: import("vue").ComputedRef<{
        focusedIndex: number;
        onFocusedIndexChange: (index: number) => void;
        onChange: (e: any) => void;
        onPressEnter: () => void;
    } | {
        onChange: (e: any) => void;
        onPressEnter: () => void;
        focusedIndex?: undefined;
        onFocusedIndexChange?: undefined;
    }>;
    panelProps: import("vue").ComputedRef<{
        displayIndex: number;
        onDisplayIndexChange: (index: number) => void;
    } | {
        displayIndex?: undefined;
        onDisplayIndexChange?: undefined;
    }>;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    onPanelVisibleChange: any;
    onInputClear: (e: Event) => void;
    onPanelSelect: (value: import("dayjs").Dayjs | (import("dayjs").Dayjs | undefined)[]) => void;
    onPanelConfirm: (value: import("dayjs").Dayjs | import("dayjs").Dayjs[]) => void;
    onPanelClick: () => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    change: (timeString: string | (string | undefined)[] | undefined, time: Date | (Date | undefined)[] | undefined) => true;
    'update:modelValue': (timeString: string | (string | undefined)[] | undefined) => true;
    select: (timeString: string | (string | undefined)[], time: Date | (Date | undefined)[]) => true;
    clear: () => true;
    'popup-visible-change': (visible: boolean) => true;
    'update:popupVisible': (visible: boolean) => true;
}, string, {
    popupVisible: boolean;
    defaultPopupVisible: boolean;
    position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
    disabled: boolean;
    unmountOnClose: boolean;
    type: "time" | "time-range";
    readonly: boolean;
    error: boolean;
    allowClear: boolean;
    format: string;
    disableConfirm: boolean;
    use12Hours: boolean;
    hideDisabledOptions: boolean;
}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type TimePickerInstance = InstanceType<typeof _TimePicker>;
export default TimePicker;
