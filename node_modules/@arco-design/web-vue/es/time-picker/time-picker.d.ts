import { PropType } from 'vue';
import { Dayjs } from 'dayjs';
import { TriggerProps } from '../trigger';
declare const _default: import("vue").DefineComponent<{
    type: {
        type: PropType<"time" | "time-range">;
        default: string;
    };
    modelValue: {
        type: PropType<string | number | Date | (string | number | Date)[]>;
    };
    defaultValue: {
        type: PropType<string | number | Date | (string | number | Date)[]>;
    };
    disabled: {
        type: BooleanConstructor;
    };
    allowClear: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
    };
    error: {
        type: BooleanConstructor;
    };
    format: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: PropType<string | string[]>;
    };
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    popupContainer: {
        type: PropType<string | HTMLElement>;
    };
    use12Hours: {
        type: BooleanConstructor;
    };
    step: {
        type: PropType<{
            hour?: number | undefined;
            minute?: number | undefined;
            second?: number | undefined;
        }>;
    };
    disabledHours: {
        type: PropType<() => number[]>;
    };
    disabledMinutes: {
        type: PropType<(selectedHour?: number | undefined) => number[]>;
    };
    disabledSeconds: {
        type: PropType<(selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]>;
    };
    hideDisabledOptions: {
        type: BooleanConstructor;
    };
    disableConfirm: {
        type: BooleanConstructor;
    };
    position: {
        type: PropType<"top" | "tl" | "tr" | "bottom" | "bl" | "br">;
        default: string;
    };
    popupVisible: {
        type: BooleanConstructor;
        default: undefined;
    };
    defaultPopupVisible: {
        type: BooleanConstructor;
        default: boolean;
    };
    triggerProps: {
        type: PropType<TriggerProps>;
    };
    unmountOnClose: {
        type: BooleanConstructor;
    };
}, {
    refInput: import("vue").Ref<any>;
    isRange: import("vue").ComputedRef<boolean>;
    prefixCls: string;
    panelVisible: import("vue").ComputedRef<boolean>;
    focusedInputIndex: import("vue").Ref<number>;
    computedPlaceholder: import("vue").ComputedRef<string | string[]>;
    panelValue: import("vue").Ref<Dayjs | (Dayjs | undefined)[] | undefined>;
    inputValue: import("vue").Ref<string | (string | undefined)[] | undefined>;
    computedFormat: import("vue").ComputedRef<string>;
    computedUse12Hours: import("vue").ComputedRef<boolean>;
    inputProps: import("vue").ComputedRef<{
        focusedIndex: number;
        onFocusedIndexChange: (index: number) => void;
        onChange: (e: any) => void;
        onPressEnter: () => void;
    } | {
        onChange: (e: any) => void;
        onPressEnter: () => void;
        focusedIndex?: undefined;
        onFocusedIndexChange?: undefined;
    }>;
    panelProps: import("vue").ComputedRef<{
        displayIndex: number;
        onDisplayIndexChange: (index: number) => void;
    } | {
        displayIndex?: undefined;
        onDisplayIndexChange?: undefined;
    }>;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    onPanelVisibleChange: any;
    onInputClear: (e: Event) => void;
    onPanelSelect: (value: Dayjs | Array<Dayjs | undefined>) => void;
    onPanelConfirm: (value: Dayjs | Dayjs[]) => void;
    onPanelClick: () => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    change: (timeString: string | Array<string | undefined> | undefined, time: Date | Array<Date | undefined> | undefined) => true;
    'update:modelValue': (timeString: string | Array<string | undefined> | undefined) => true;
    select: (timeString: string | Array<string | undefined>, time: Date | Array<Date | undefined>) => true;
    clear: () => true;
    'popup-visible-change': (visible: boolean) => true;
    'update:popupVisible': (visible: boolean) => true;
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    type?: unknown;
    modelValue?: unknown;
    defaultValue?: unknown;
    disabled?: unknown;
    allowClear?: unknown;
    readonly?: unknown;
    error?: unknown;
    format?: unknown;
    placeholder?: unknown;
    size?: unknown;
    popupContainer?: unknown;
    use12Hours?: unknown;
    step?: unknown;
    disabledHours?: unknown;
    disabledMinutes?: unknown;
    disabledSeconds?: unknown;
    hideDisabledOptions?: unknown;
    disableConfirm?: unknown;
    position?: unknown;
    popupVisible?: unknown;
    defaultPopupVisible?: unknown;
    triggerProps?: unknown;
    unmountOnClose?: unknown;
} & {
    defaultPopupVisible: boolean;
    position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
    disabled: boolean;
    unmountOnClose: boolean;
    type: "time" | "time-range";
    readonly: boolean;
    error: boolean;
    allowClear: boolean;
    format: string;
    disableConfirm: boolean;
    use12Hours: boolean;
    hideDisabledOptions: boolean;
} & {
    popupVisible?: boolean | undefined;
    popupContainer?: string | HTMLElement | undefined;
    size?: "mini" | "medium" | "large" | "small" | undefined;
    placeholder?: string | string[] | undefined;
    defaultValue?: string | number | Date | (string | number | Date)[] | undefined;
    modelValue?: string | number | Date | (string | number | Date)[] | undefined;
    triggerProps?: TriggerProps | undefined;
    step?: {
        hour?: number | undefined;
        minute?: number | undefined;
        second?: number | undefined;
    } | undefined;
    disabledHours?: (() => number[]) | undefined;
    disabledMinutes?: ((selectedHour?: number | undefined) => number[]) | undefined;
    disabledSeconds?: ((selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]) | undefined;
}> & {
    "onUpdate:popupVisible"?: ((visible: boolean) => any) | undefined;
    onChange?: ((timeString: string | (string | undefined)[] | undefined, time: Date | (Date | undefined)[] | undefined) => any) | undefined;
    onSelect?: ((timeString: string | (string | undefined)[], time: Date | (Date | undefined)[]) => any) | undefined;
    onClear?: (() => any) | undefined;
    "onUpdate:modelValue"?: ((timeString: string | (string | undefined)[] | undefined) => any) | undefined;
    "onPopup-visible-change"?: ((visible: boolean) => any) | undefined;
}, {
    popupVisible: boolean;
    defaultPopupVisible: boolean;
    position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
    disabled: boolean;
    unmountOnClose: boolean;
    type: "time" | "time-range";
    readonly: boolean;
    error: boolean;
    allowClear: boolean;
    format: string;
    disableConfirm: boolean;
    use12Hours: boolean;
    hideDisabledOptions: boolean;
}>;
export default _default;
