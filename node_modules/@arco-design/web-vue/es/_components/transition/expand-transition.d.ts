declare const _default: import("vue").DefineComponent<{}, {
    onBeforeEnter(el: HTMLDivElement): void;
    onEnter(el: HTMLDivElement): void;
    onAfterEnter(el: HTMLDivElement): void;
    onBeforeLeave(el: HTMLDivElement): void;
    onLeave(el: HTMLDivElement): void;
    onAfterLeave(el: HTMLElement): void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, import("vue").EmitsOptions, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{} & {} & {}>, {}>;
export default _default;
