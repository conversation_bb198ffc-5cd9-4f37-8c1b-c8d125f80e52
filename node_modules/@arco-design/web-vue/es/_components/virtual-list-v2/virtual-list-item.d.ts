declare const _default: import("vue").DefineComponent<{
    hasItemSize: {
        type: FunctionConstructor;
        required: true;
    };
    setItemSize: {
        type: FunctionConstructor;
        required: true;
    };
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}> | null, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    hasItemSize?: unknown;
    setItemSize?: unknown;
} & {
    hasItemSize: Function;
    setItemSize: Function;
} & {}>, {}>;
export default _default;
