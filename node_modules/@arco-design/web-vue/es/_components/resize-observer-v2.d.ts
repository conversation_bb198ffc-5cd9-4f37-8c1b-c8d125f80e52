declare const _default: import("vue").DefineComponent<{
    watchOnUpdated: BooleanConstructor;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>[] | undefined, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "resize"[], "resize", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    watchOnUpdated?: unknown;
} & {
    watchOnUpdated: boolean;
} & {}> & {
    onResize?: ((...args: any[]) => any) | undefined;
}, {
    watchOnUpdated: boolean;
}>;
export default _default;
