/******** 基础配置项 end *******/
/******** 基础配置项 end *******/
/******** 高级配置项 *******/
/******** 高级配置项 end *******/
.arco-tag {
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
  height: 24px;
  padding: 0 8px;
  color: var(--color-text-1);
  font-weight: 500;
  font-size: 12px;
  line-height: 22px;
  vertical-align: middle;
  border: 1px solid transparent;
  border-radius: var(--border-radius-small);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-tag .arco-icon-hover.arco-tag-icon-hover::before {
  width: 16px;
  height: 16px;
}
.arco-tag .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: var(--color-fill-3);
}
.arco-tag-checkable {
  cursor: pointer;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-tag-checkable:hover {
  background-color: var(--color-fill-2);
}
.arco-tag-checked {
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-tag-checkable.arco-tag-checked:hover {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-tag-bordered,
.arco-tag-checkable.arco-tag-checked.arco-tag-bordered:hover {
  border-color: var(--color-border-2);
}
.arco-tag-size-small {
  height: 20px;
  font-size: 12px;
  line-height: 18px;
}
.arco-tag-size-medium {
  height: 24px;
  font-size: 12px;
  line-height: 22px;
}
.arco-tag-size-large {
  height: 32px;
  font-size: 14px;
  line-height: 30px;
}
.arco-tag-hide {
  display: none;
}
.arco-tag-loading {
  cursor: default;
  opacity: 0.8;
}
.arco-tag-icon {
  margin-right: 4px;
  color: var(--color-text-2);
}
.arco-tag-text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-tag.arco-tag-checked.arco-tag-red {
  color: rgb(var(--red-6));
  background-color: rgb(var(--red-1));
  border: 1px solid transparent;
}
.arco-tag.arco-tag-checked.arco-tag-red .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgb(var(--red-2));
}
.arco-tag.arco-tag-checkable.arco-tag-checked.arco-tag-red.arco-tag:hover {
  background-color: rgb(var(--red-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-red.arco-tag-bordered,
.arco-tag-checked.arco-tag-red.arco-tag-bordered:hover {
  border-color: rgb(var(--red-6));
}
.arco-tag.arco-tag-checked.arco-tag-red .arco-tag-icon,
.arco-tag.arco-tag-checked.arco-tag-red .arco-tag-close-btn,
.arco-tag.arco-tag-checked.arco-tag-red .arco-tag-loading-icon {
  color: rgb(var(--red-6));
}
.arco-tag.arco-tag-checked.arco-tag-orangered {
  color: rgb(var(--orangered-6));
  background-color: rgb(var(--orangered-1));
  border: 1px solid transparent;
}
.arco-tag.arco-tag-checked.arco-tag-orangered .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgb(var(--orangered-2));
}
.arco-tag.arco-tag-checkable.arco-tag-checked.arco-tag-orangered.arco-tag:hover {
  background-color: rgb(var(--orangered-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-orangered.arco-tag-bordered,
.arco-tag-checked.arco-tag-orangered.arco-tag-bordered:hover {
  border-color: rgb(var(--orangered-6));
}
.arco-tag.arco-tag-checked.arco-tag-orangered .arco-tag-icon,
.arco-tag.arco-tag-checked.arco-tag-orangered .arco-tag-close-btn,
.arco-tag.arco-tag-checked.arco-tag-orangered .arco-tag-loading-icon {
  color: rgb(var(--orangered-6));
}
.arco-tag.arco-tag-checked.arco-tag-orange {
  color: rgb(var(--orange-6));
  background-color: rgb(var(--orange-1));
  border: 1px solid transparent;
}
.arco-tag.arco-tag-checked.arco-tag-orange .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgb(var(--orange-2));
}
.arco-tag.arco-tag-checkable.arco-tag-checked.arco-tag-orange.arco-tag:hover {
  background-color: rgb(var(--orange-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-orange.arco-tag-bordered,
.arco-tag-checked.arco-tag-orange.arco-tag-bordered:hover {
  border-color: rgb(var(--orange-6));
}
.arco-tag.arco-tag-checked.arco-tag-orange .arco-tag-icon,
.arco-tag.arco-tag-checked.arco-tag-orange .arco-tag-close-btn,
.arco-tag.arco-tag-checked.arco-tag-orange .arco-tag-loading-icon {
  color: rgb(var(--orange-6));
}
.arco-tag.arco-tag-checked.arco-tag-gold {
  color: rgb(var(--gold-6));
  background-color: rgb(var(--gold-1));
  border: 1px solid transparent;
}
.arco-tag.arco-tag-checked.arco-tag-gold .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgb(var(--gold-2));
}
.arco-tag.arco-tag-checkable.arco-tag-checked.arco-tag-gold.arco-tag:hover {
  background-color: rgb(var(--gold-3));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-gold.arco-tag-bordered,
.arco-tag-checked.arco-tag-gold.arco-tag-bordered:hover {
  border-color: rgb(var(--gold-6));
}
.arco-tag.arco-tag-checked.arco-tag-gold .arco-tag-icon,
.arco-tag.arco-tag-checked.arco-tag-gold .arco-tag-close-btn,
.arco-tag.arco-tag-checked.arco-tag-gold .arco-tag-loading-icon {
  color: rgb(var(--gold-6));
}
.arco-tag.arco-tag-checked.arco-tag-lime {
  color: rgb(var(--lime-6));
  background-color: rgb(var(--lime-1));
  border: 1px solid transparent;
}
.arco-tag.arco-tag-checked.arco-tag-lime .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgb(var(--lime-2));
}
.arco-tag.arco-tag-checkable.arco-tag-checked.arco-tag-lime.arco-tag:hover {
  background-color: rgb(var(--lime-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-lime.arco-tag-bordered,
.arco-tag-checked.arco-tag-lime.arco-tag-bordered:hover {
  border-color: rgb(var(--lime-6));
}
.arco-tag.arco-tag-checked.arco-tag-lime .arco-tag-icon,
.arco-tag.arco-tag-checked.arco-tag-lime .arco-tag-close-btn,
.arco-tag.arco-tag-checked.arco-tag-lime .arco-tag-loading-icon {
  color: rgb(var(--lime-6));
}
.arco-tag.arco-tag-checked.arco-tag-green {
  color: rgb(var(--green-6));
  background-color: rgb(var(--green-1));
  border: 1px solid transparent;
}
.arco-tag.arco-tag-checked.arco-tag-green .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgb(var(--green-2));
}
.arco-tag.arco-tag-checkable.arco-tag-checked.arco-tag-green.arco-tag:hover {
  background-color: rgb(var(--green-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-green.arco-tag-bordered,
.arco-tag-checked.arco-tag-green.arco-tag-bordered:hover {
  border-color: rgb(var(--green-6));
}
.arco-tag.arco-tag-checked.arco-tag-green .arco-tag-icon,
.arco-tag.arco-tag-checked.arco-tag-green .arco-tag-close-btn,
.arco-tag.arco-tag-checked.arco-tag-green .arco-tag-loading-icon {
  color: rgb(var(--green-6));
}
.arco-tag.arco-tag-checked.arco-tag-cyan {
  color: rgb(var(--cyan-6));
  background-color: rgb(var(--cyan-1));
  border: 1px solid transparent;
}
.arco-tag.arco-tag-checked.arco-tag-cyan .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgb(var(--cyan-2));
}
.arco-tag.arco-tag-checkable.arco-tag-checked.arco-tag-cyan.arco-tag:hover {
  background-color: rgb(var(--cyan-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-cyan.arco-tag-bordered,
.arco-tag-checked.arco-tag-cyan.arco-tag-bordered:hover {
  border-color: rgb(var(--cyan-6));
}
.arco-tag.arco-tag-checked.arco-tag-cyan .arco-tag-icon,
.arco-tag.arco-tag-checked.arco-tag-cyan .arco-tag-close-btn,
.arco-tag.arco-tag-checked.arco-tag-cyan .arco-tag-loading-icon {
  color: rgb(var(--cyan-6));
}
.arco-tag.arco-tag-checked.arco-tag-blue {
  color: rgb(var(--blue-6));
  background-color: rgb(var(--blue-1));
  border: 1px solid transparent;
}
.arco-tag.arco-tag-checked.arco-tag-blue .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgb(var(--blue-2));
}
.arco-tag.arco-tag-checkable.arco-tag-checked.arco-tag-blue.arco-tag:hover {
  background-color: rgb(var(--blue-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-blue.arco-tag-bordered,
.arco-tag-checked.arco-tag-blue.arco-tag-bordered:hover {
  border-color: rgb(var(--blue-6));
}
.arco-tag.arco-tag-checked.arco-tag-blue .arco-tag-icon,
.arco-tag.arco-tag-checked.arco-tag-blue .arco-tag-close-btn,
.arco-tag.arco-tag-checked.arco-tag-blue .arco-tag-loading-icon {
  color: rgb(var(--blue-6));
}
.arco-tag.arco-tag-checked.arco-tag-arcoblue {
  color: rgb(var(--arcoblue-6));
  background-color: rgb(var(--arcoblue-1));
  border: 1px solid transparent;
}
.arco-tag.arco-tag-checked.arco-tag-arcoblue .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgb(var(--arcoblue-2));
}
.arco-tag.arco-tag-checkable.arco-tag-checked.arco-tag-arcoblue.arco-tag:hover {
  background-color: rgb(var(--arcoblue-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-arcoblue.arco-tag-bordered,
.arco-tag-checked.arco-tag-arcoblue.arco-tag-bordered:hover {
  border-color: rgb(var(--arcoblue-6));
}
.arco-tag.arco-tag-checked.arco-tag-arcoblue .arco-tag-icon,
.arco-tag.arco-tag-checked.arco-tag-arcoblue .arco-tag-close-btn,
.arco-tag.arco-tag-checked.arco-tag-arcoblue .arco-tag-loading-icon {
  color: rgb(var(--arcoblue-6));
}
.arco-tag.arco-tag-checked.arco-tag-purple {
  color: rgb(var(--purple-6));
  background-color: rgb(var(--purple-1));
  border: 1px solid transparent;
}
.arco-tag.arco-tag-checked.arco-tag-purple .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgb(var(--purple-2));
}
.arco-tag.arco-tag-checkable.arco-tag-checked.arco-tag-purple.arco-tag:hover {
  background-color: rgb(var(--purple-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-purple.arco-tag-bordered,
.arco-tag-checked.arco-tag-purple.arco-tag-bordered:hover {
  border-color: rgb(var(--purple-6));
}
.arco-tag.arco-tag-checked.arco-tag-purple .arco-tag-icon,
.arco-tag.arco-tag-checked.arco-tag-purple .arco-tag-close-btn,
.arco-tag.arco-tag-checked.arco-tag-purple .arco-tag-loading-icon {
  color: rgb(var(--purple-6));
}
.arco-tag.arco-tag-checked.arco-tag-pinkpurple {
  color: rgb(var(--pinkpurple-6));
  background-color: rgb(var(--pinkpurple-1));
  border: 1px solid transparent;
}
.arco-tag.arco-tag-checked.arco-tag-pinkpurple .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgb(var(--pinkpurple-2));
}
.arco-tag.arco-tag-checkable.arco-tag-checked.arco-tag-pinkpurple.arco-tag:hover {
  background-color: rgb(var(--pinkpurple-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-pinkpurple.arco-tag-bordered,
.arco-tag-checked.arco-tag-pinkpurple.arco-tag-bordered:hover {
  border-color: rgb(var(--pinkpurple-6));
}
.arco-tag.arco-tag-checked.arco-tag-pinkpurple .arco-tag-icon,
.arco-tag.arco-tag-checked.arco-tag-pinkpurple .arco-tag-close-btn,
.arco-tag.arco-tag-checked.arco-tag-pinkpurple .arco-tag-loading-icon {
  color: rgb(var(--pinkpurple-6));
}
.arco-tag.arco-tag-checked.arco-tag-magenta {
  color: rgb(var(--magenta-6));
  background-color: rgb(var(--magenta-1));
  border: 1px solid transparent;
}
.arco-tag.arco-tag-checked.arco-tag-magenta .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgb(var(--magenta-2));
}
.arco-tag.arco-tag-checkable.arco-tag-checked.arco-tag-magenta.arco-tag:hover {
  background-color: rgb(var(--magenta-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-magenta.arco-tag-bordered,
.arco-tag-checked.arco-tag-magenta.arco-tag-bordered:hover {
  border-color: rgb(var(--magenta-6));
}
.arco-tag.arco-tag-checked.arco-tag-magenta .arco-tag-icon,
.arco-tag.arco-tag-checked.arco-tag-magenta .arco-tag-close-btn,
.arco-tag.arco-tag-checked.arco-tag-magenta .arco-tag-loading-icon {
  color: rgb(var(--magenta-6));
}
.arco-tag.arco-tag-checked.arco-tag-gray {
  color: rgb(var(--gray-6));
  background-color: rgb(var(--gray-2));
  border: 1px solid transparent;
}
.arco-tag.arco-tag-checked.arco-tag-gray .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgb(var(--gray-3));
}
.arco-tag.arco-tag-checkable.arco-tag-checked.arco-tag-gray.arco-tag:hover {
  background-color: rgb(var(--gray-3));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-gray.arco-tag-bordered,
.arco-tag-checked.arco-tag-gray.arco-tag-bordered:hover {
  border-color: rgb(var(--gray-6));
}
.arco-tag.arco-tag-checked.arco-tag-gray .arco-tag-icon,
.arco-tag.arco-tag-checked.arco-tag-gray .arco-tag-close-btn,
.arco-tag.arco-tag-checked.arco-tag-gray .arco-tag-loading-icon {
  color: rgb(var(--gray-6));
}
.arco-tag.arco-tag-custom-color {
  color: var(--color-white);
}
.arco-tag.arco-tag-custom-color .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(255, 255, 255, 0.2);
}
.arco-tag .arco-tag-close-btn {
  margin-left: 4px;
  font-size: 12px;
}
.arco-tag .arco-tag-close-btn > svg {
  position: relative;
}
.arco-tag .arco-tag-loading-icon {
  margin-left: 4px;
  font-size: 12px;
}
body[arco-theme='dark'] .arco-tag-checked {
  color: rgba(255, 255, 255, 0.9);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-red {
  background-color: rgba(var(--red-6), 0.2);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-red .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(var(--red-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checkable.arco-tag-checked.arco-tag-red:hover {
  background-color: rgba(var(--red-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-orangered {
  background-color: rgba(var(--orangered-6), 0.2);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-orangered .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(var(--orangered-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checkable.arco-tag-checked.arco-tag-orangered:hover {
  background-color: rgba(var(--orangered-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-orange {
  background-color: rgba(var(--orange-6), 0.2);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-orange .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(var(--orange-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checkable.arco-tag-checked.arco-tag-orange:hover {
  background-color: rgba(var(--orange-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-gold {
  background-color: rgba(var(--gold-6), 0.2);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-gold .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(var(--gold-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checkable.arco-tag-checked.arco-tag-gold:hover {
  background-color: rgba(var(--gold-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-lime {
  background-color: rgba(var(--lime-6), 0.2);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-lime .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(var(--lime-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checkable.arco-tag-checked.arco-tag-lime:hover {
  background-color: rgba(var(--lime-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-green {
  background-color: rgba(var(--green-6), 0.2);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-green .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(var(--green-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checkable.arco-tag-checked.arco-tag-green:hover {
  background-color: rgba(var(--green-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-cyan {
  background-color: rgba(var(--cyan-6), 0.2);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-cyan .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(var(--cyan-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checkable.arco-tag-checked.arco-tag-cyan:hover {
  background-color: rgba(var(--cyan-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-blue {
  background-color: rgba(var(--blue-6), 0.2);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-blue .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(var(--blue-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checkable.arco-tag-checked.arco-tag-blue:hover {
  background-color: rgba(var(--blue-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-arcoblue {
  background-color: rgba(var(--arcoblue-6), 0.2);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-arcoblue .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(var(--arcoblue-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checkable.arco-tag-checked.arco-tag-arcoblue:hover {
  background-color: rgba(var(--arcoblue-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-purple {
  background-color: rgba(var(--purple-6), 0.2);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-purple .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(var(--purple-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checkable.arco-tag-checked.arco-tag-purple:hover {
  background-color: rgba(var(--purple-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-pinkpurple {
  background-color: rgba(var(--pinkpurple-6), 0.2);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-pinkpurple .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(var(--pinkpurple-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checkable.arco-tag-checked.arco-tag-pinkpurple:hover {
  background-color: rgba(var(--pinkpurple-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-magenta {
  background-color: rgba(var(--magenta-6), 0.2);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-magenta .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(var(--magenta-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checkable.arco-tag-checked.arco-tag-magenta:hover {
  background-color: rgba(var(--magenta-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-gray {
  background-color: rgba(var(--gray-6), 0.2);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-gray .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(var(--gray-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checkable.arco-tag-checked.arco-tag-gray:hover {
  background-color: rgba(var(--gray-6), 0.35);
}
/**********************************************
 * size: mini / small / default / large
 **********************************************/
/****************************************************
 * status: default / error / disabled _ (hover / focus)
 ****************************************************/
/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
.arco-select-view-single {
  display: inline-flex;
  box-sizing: border-box;
  width: 100%;
  padding-right: 12px;
  padding-left: 12px;
  color: var(--color-text-1);
  font-size: 14px;
  background-color: var(--color-fill-2);
  border: 1px solid transparent;
  border-radius: var(--border-radius-small);
  cursor: text;
  transition: color 0.1s cubic-bezier(0, 0, 1, 1), border-color 0.1s cubic-bezier(0, 0, 1, 1), background-color 0.1s cubic-bezier(0, 0, 1, 1);
  cursor: pointer;
}
.arco-select-view-single.arco-select-view-search {
  cursor: text;
}
.arco-select-view-single.arco-select-view-search .arco-select-view-value {
  pointer-events: none;
}
.arco-select-view-single:hover {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-select-view-single:focus-within,
.arco-select-view-single.arco-select-view-focus {
  z-index: 1;
  background-color: var(--color-bg-2);
  border-color: rgb(var(--primary-6));
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-select-view-single.arco-select-view-disabled {
  color: var(--color-text-4);
  background-color: var(--color-fill-2);
  border-color: transparent;
  cursor: not-allowed;
}
.arco-select-view-single.arco-select-view-disabled:hover {
  color: var(--color-text-4);
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-select-view-single.arco-select-view-disabled .arco-select-view-prefix,
.arco-select-view-single.arco-select-view-disabled .arco-select-view-suffix {
  color: inherit;
}
.arco-select-view-single.arco-select-view-error {
  background-color: var(--color-danger-light-1);
  border-color: transparent;
}
.arco-select-view-single.arco-select-view-error:hover {
  background-color: var(--color-danger-light-2);
  border-color: transparent;
}
.arco-select-view-single.arco-select-view-error:focus-within,
.arco-select-view-single.arco-select-view-error.arco-select-view-single-focus {
  z-index: 1;
  background-color: var(--color-bg-2);
  border-color: rgb(var(--danger-6));
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-select-view-single .arco-select-view-prefix,
.arco-select-view-single .arco-select-view-suffix {
  display: inline-flex;
  flex-shrink: 0;
  align-items: center;
  white-space: nowrap;
  user-select: none;
}
.arco-select-view-single .arco-select-view-prefix > svg,
.arco-select-view-single .arco-select-view-suffix > svg {
  font-size: 14px;
}
.arco-select-view-single .arco-select-view-prefix {
  padding-right: 12px;
  color: var(--color-text-2);
}
.arco-select-view-single .arco-select-view-suffix {
  padding-left: 12px;
  color: var(--color-text-2);
}
.arco-select-view-single .arco-select-view-suffix .arco-feedback-icon {
  display: inline-flex;
}
.arco-select-view-single .arco-select-view-suffix .arco-feedback-icon-status-validating {
  color: rgb(var(--primary-6));
}
.arco-select-view-single .arco-select-view-suffix .arco-feedback-icon-status-success {
  color: rgb(var(--success-6));
}
.arco-select-view-single .arco-select-view-suffix .arco-feedback-icon-status-warning {
  color: rgb(var(--warning-6));
}
.arco-select-view-single .arco-select-view-suffix .arco-feedback-icon-status-error {
  color: rgb(var(--danger-6));
}
.arco-select-view-single .arco-select-view-clear-btn {
  align-self: center;
  color: var(--color-text-2);
  font-size: 12px;
  visibility: hidden;
  cursor: pointer;
}
.arco-select-view-single .arco-select-view-clear-btn > svg {
  position: relative;
  transition: color 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-select-view-single:hover .arco-select-view-clear-btn {
  visibility: visible;
}
.arco-select-view-single:not(.arco-select-view-focus) .arco-select-view-icon-hover:hover::before {
  background-color: var(--color-fill-4);
}
.arco-select-view-single .arco-select-view-input {
  width: 100%;
  padding-right: 0;
  padding-left: 0;
  color: inherit;
  line-height: 1.5715;
  background: none;
  border: none;
  border-radius: 0;
  outline: none;
  cursor: inherit;
  -webkit-appearance: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.arco-select-view-single .arco-select-view-input::placeholder {
  color: var(--color-text-3);
}
.arco-select-view-single .arco-select-view-input[disabled]::placeholder {
  color: var(--color-text-4);
}
.arco-select-view-single .arco-select-view-input[disabled] {
  -webkit-text-fill-color: var(--color-text-4);
}
.arco-select-view-single .arco-select-view-input-hidden {
  position: absolute;
  width: 0 !important;
}
.arco-select-view-single .arco-select-view-value {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-select-view-single .arco-select-view-value::after {
  font-size: 0;
  line-height: 0;
  visibility: hidden;
  content: '.';
}
.arco-select-view-single .arco-select-view-value-hidden {
  display: none;
}
.arco-select-view-single.arco-select-view-size-mini .arco-select-view-input,
.arco-select-view-single.arco-select-view-size-mini .arco-select-view-value {
  /* prettier-ignore */
  padding-top: 1px;
  /* prettier-ignore */
  padding-bottom: 1px;
  font-size: 12px;
  line-height: 1.667;
}
.arco-select-view-single.arco-select-view-size-mini .arco-select-view-value {
  min-height: 22px;
}
.arco-select-view-single.arco-select-view-size-medium .arco-select-view-input,
.arco-select-view-single.arco-select-view-size-medium .arco-select-view-value {
  /* prettier-ignore */
  padding-top: 4px;
  /* prettier-ignore */
  padding-bottom: 4px;
  font-size: 14px;
  line-height: 1.5715;
}
.arco-select-view-single.arco-select-view-size-medium .arco-select-view-value {
  min-height: 30px;
}
.arco-select-view-single.arco-select-view-size-small .arco-select-view-input,
.arco-select-view-single.arco-select-view-size-small .arco-select-view-value {
  /* prettier-ignore */
  padding-top: 2px;
  /* prettier-ignore */
  padding-bottom: 2px;
  font-size: 14px;
  line-height: 1.5715;
}
.arco-select-view-single.arco-select-view-size-small .arco-select-view-value {
  min-height: 26px;
}
.arco-select-view-single.arco-select-view-size-large .arco-select-view-input,
.arco-select-view-single.arco-select-view-size-large .arco-select-view-value {
  /* prettier-ignore */
  padding-top: 6px;
  /* prettier-ignore */
  padding-bottom: 6px;
  font-size: 14px;
  line-height: 1.5715;
}
.arco-select-view-single.arco-select-view-size-large .arco-select-view-value {
  min-height: 34px;
}
.arco-select-view-multiple {
  display: inline-flex;
  box-sizing: border-box;
  width: 100%;
  padding-right: 12px;
  padding-left: 12px;
  color: var(--color-text-1);
  font-size: 14px;
  background-color: var(--color-fill-2);
  border: 1px solid transparent;
  border-radius: var(--border-radius-small);
  cursor: text;
  transition: color 0.1s cubic-bezier(0, 0, 1, 1), border-color 0.1s cubic-bezier(0, 0, 1, 1), background-color 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-select-view-multiple:hover {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-select-view-multiple:focus-within,
.arco-select-view-multiple.arco-select-view-focus {
  z-index: 1;
  background-color: var(--color-bg-2);
  border-color: rgb(var(--primary-6));
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-select-view-multiple.arco-select-view-disabled {
  color: var(--color-text-4);
  background-color: var(--color-fill-2);
  border-color: transparent;
  cursor: not-allowed;
}
.arco-select-view-multiple.arco-select-view-disabled:hover {
  color: var(--color-text-4);
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-select-view-multiple.arco-select-view-disabled .arco-select-view-prefix,
.arco-select-view-multiple.arco-select-view-disabled .arco-select-view-suffix {
  color: inherit;
}
.arco-select-view-multiple.arco-select-view-error {
  background-color: var(--color-danger-light-1);
  border-color: transparent;
}
.arco-select-view-multiple.arco-select-view-error:hover {
  background-color: var(--color-danger-light-2);
  border-color: transparent;
}
.arco-select-view-multiple.arco-select-view-error:focus-within,
.arco-select-view-multiple.arco-select-view-error.arco-select-view-multiple-focus {
  z-index: 1;
  background-color: var(--color-bg-2);
  border-color: rgb(var(--danger-6));
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-select-view-multiple .arco-select-view-prefix,
.arco-select-view-multiple .arco-select-view-suffix {
  display: inline-flex;
  flex-shrink: 0;
  align-items: center;
  white-space: nowrap;
  user-select: none;
}
.arco-select-view-multiple .arco-select-view-prefix > svg,
.arco-select-view-multiple .arco-select-view-suffix > svg {
  font-size: 14px;
}
.arco-select-view-multiple .arco-select-view-prefix {
  padding-right: 12px;
  color: var(--color-text-2);
}
.arco-select-view-multiple .arco-select-view-suffix {
  padding-left: 12px;
  color: var(--color-text-2);
}
.arco-select-view-multiple .arco-select-view-suffix .arco-feedback-icon {
  display: inline-flex;
}
.arco-select-view-multiple .arco-select-view-suffix .arco-feedback-icon-status-validating {
  color: rgb(var(--primary-6));
}
.arco-select-view-multiple .arco-select-view-suffix .arco-feedback-icon-status-success {
  color: rgb(var(--success-6));
}
.arco-select-view-multiple .arco-select-view-suffix .arco-feedback-icon-status-warning {
  color: rgb(var(--warning-6));
}
.arco-select-view-multiple .arco-select-view-suffix .arco-feedback-icon-status-error {
  color: rgb(var(--danger-6));
}
.arco-select-view-multiple .arco-select-view-clear-btn {
  align-self: center;
  color: var(--color-text-2);
  font-size: 12px;
  visibility: hidden;
  cursor: pointer;
}
.arco-select-view-multiple .arco-select-view-clear-btn > svg {
  position: relative;
  transition: color 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-select-view-multiple:hover .arco-select-view-clear-btn {
  visibility: visible;
}
.arco-select-view-multiple:not(.arco-select-view-focus) .arco-select-view-icon-hover:hover::before {
  background-color: var(--color-fill-4);
}
.arco-select-view-multiple.arco-select-view-has-tag {
  padding-right: 4px;
  padding-left: 4px;
}
.arco-select-view-multiple.arco-select-view-has-prefix {
  padding-left: 12px;
}
.arco-select-view-multiple.arco-select-view-has-suffix {
  padding-right: 12px;
}
.arco-select-view-multiple .arco-select-view-inner {
  flex: 1;
  overflow: hidden;
  line-height: 0;
}
.arco-select-view-multiple .arco-select-view-inner.arco-select-view-nowrap {
  display: flex;
  flex-wrap: wrap;
}
.arco-select-view-multiple .arco-select-view-inner .arco-select-view-tag {
  display: inline-flex;
  align-items: center;
  margin-right: 4px;
  color: var(--color-text-1);
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-word;
  background-color: var(--color-bg-2);
  border-color: var(--color-fill-3);
}
.arco-select-view-multiple .arco-select-view-inner .arco-select-view-tag .arco-icon-hover:hover::before {
  background-color: var(--color-fill-2);
}
.arco-select-view-multiple .arco-select-view-inner .arco-select-view-tag.arco-tag-custom-color {
  color: var(--color-white);
}
.arco-select-view-multiple .arco-select-view-inner .arco-select-view-tag.arco-tag-custom-color .arco-icon-hover.arco-tag-icon-hover:hover::before {
  background-color: rgba(255, 255, 255, 0.2);
}
.arco-select-view-multiple .arco-select-view-inner .arco-select-view-input {
  width: 100%;
  padding-right: 0;
  padding-left: 0;
  color: inherit;
  line-height: 1.5715;
  background: none;
  border: none;
  border-radius: 0;
  outline: none;
  cursor: inherit;
  -webkit-appearance: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  box-sizing: border-box;
}
.arco-select-view-multiple .arco-select-view-inner .arco-select-view-input::placeholder {
  color: var(--color-text-3);
}
.arco-select-view-multiple .arco-select-view-inner .arco-select-view-input[disabled]::placeholder {
  color: var(--color-text-4);
}
.arco-select-view-multiple .arco-select-view-inner .arco-select-view-input[disabled] {
  -webkit-text-fill-color: var(--color-text-4);
}
.arco-select-view-multiple .arco-select-view-mirror {
  position: absolute;
  top: 0;
  left: 0;
  white-space: pre;
  visibility: hidden;
  pointer-events: none;
}
.arco-select-view-multiple.arco-select-view-focus .arco-select-view-tag {
  background-color: var(--color-fill-2);
  border-color: var(--color-fill-2);
}
.arco-select-view-multiple.arco-select-view-focus .arco-select-view-tag .arco-icon-hover:hover::before {
  background-color: var(--color-fill-3);
}
.arco-select-view-multiple.arco-select-view-disabled .arco-select-view-tag {
  color: var(--color-text-4);
  background-color: var(--color-fill-2);
  border-color: var(--color-fill-3);
}
.arco-select-view-multiple.arco-select-view-readonly,
.arco-select-view-multiple.arco-select-view-disabled-input {
  cursor: default;
}
.arco-select-view-multiple.arco-select-view-size-mini {
  font-size: 12px;
}
.arco-select-view-multiple.arco-select-view-size-mini .arco-select-view-inner {
  padding-top: 0px;
  padding-bottom: 0px;
}
.arco-select-view-multiple.arco-select-view-size-mini .arco-select-view-tag,
.arco-select-view-multiple.arco-select-view-size-mini .arco-select-view-input {
  margin-top: 1px;
  margin-bottom: 1px;
  line-height: 18px;
  vertical-align: middle;
}
.arco-select-view-multiple.arco-select-view-size-mini .arco-select-view-tag,
.arco-select-view-multiple.arco-select-view-size-mini .arco-select-view-input {
  height: auto;
  min-height: 20px;
}
.arco-select-view-multiple.arco-select-view-size-medium {
  font-size: 14px;
}
.arco-select-view-multiple.arco-select-view-size-medium .arco-select-view-inner {
  padding-top: 2px;
  padding-bottom: 2px;
}
.arco-select-view-multiple.arco-select-view-size-medium .arco-select-view-tag,
.arco-select-view-multiple.arco-select-view-size-medium .arco-select-view-input {
  margin-top: 1px;
  margin-bottom: 1px;
  line-height: 22px;
  vertical-align: middle;
}
.arco-select-view-multiple.arco-select-view-size-medium .arco-select-view-tag,
.arco-select-view-multiple.arco-select-view-size-medium .arco-select-view-input {
  height: auto;
  min-height: 24px;
}
.arco-select-view-multiple.arco-select-view-size-small {
  font-size: 14px;
}
.arco-select-view-multiple.arco-select-view-size-small .arco-select-view-inner {
  padding-top: 2px;
  padding-bottom: 2px;
}
.arco-select-view-multiple.arco-select-view-size-small .arco-select-view-tag,
.arco-select-view-multiple.arco-select-view-size-small .arco-select-view-input {
  margin-top: 1px;
  margin-bottom: 1px;
  line-height: 18px;
  vertical-align: middle;
}
.arco-select-view-multiple.arco-select-view-size-small .arco-select-view-tag,
.arco-select-view-multiple.arco-select-view-size-small .arco-select-view-input {
  height: auto;
  min-height: 20px;
}
.arco-select-view-multiple.arco-select-view-size-large {
  font-size: 14px;
}
.arco-select-view-multiple.arco-select-view-size-large .arco-select-view-inner {
  padding-top: 2px;
  padding-bottom: 2px;
}
.arco-select-view-multiple.arco-select-view-size-large .arco-select-view-tag,
.arco-select-view-multiple.arco-select-view-size-large .arco-select-view-input {
  margin-top: 1px;
  margin-bottom: 1px;
  line-height: 26px;
  vertical-align: middle;
}
.arco-select-view-multiple.arco-select-view-size-large .arco-select-view-tag,
.arco-select-view-multiple.arco-select-view-size-large .arco-select-view-input {
  height: auto;
  min-height: 28px;
}
.arco-select-view-multiple.arco-select-view-disabled-input {
  cursor: pointer;
}
.arco-select-view.arco-select-view-borderless {
  background: none !important;
  border: none !important;
  box-shadow: none !important;
}
.arco-select-view-suffix .arco-feedback-icon {
  margin-left: 4px;
}
.arco-select-view-clear-btn svg,
.arco-select-view-icon svg {
  display: block;
  font-size: 12px;
}
.arco-select-view-opened .arco-select-view-arrow-icon {
  transform: rotateZ(180deg);
}
.arco-select-view-expand-icon {
  transform: rotate(-45deg);
}
.arco-select-view-clear-btn {
  display: none;
  cursor: pointer;
}
.arco-select-view:hover .arco-select-view-clear-btn {
  display: block;
}
.arco-select-view:hover .arco-select-view-clear-btn ~ * {
  display: none;
}
