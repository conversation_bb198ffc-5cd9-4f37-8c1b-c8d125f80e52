@import '../../input-label/style/input-label.less';
@import '../../../input-tag/style/input-tag.less';
@import './token.less';

@select-view-prefix-cls: ~'@{prefix}-select-view';

.@{select-view-prefix-cls} {
  &-single {
    .input-label-style(@select-view-prefix-cls);
  }

  &-multiple {
    .input-tag-style(@select-view-prefix-cls);

    &.@{select-view-prefix-cls}-disabled-input {
      cursor: pointer;
    }
  }

  &&-borderless {
    background: none !important;
    border: none !important;
    box-shadow: none !important;
  }

  &-suffix .@{prefix}-feedback-icon {
    margin-left: 4px;
  }

  &-clear-btn,
  &-icon {
    svg {
      display: block;
      font-size: @select-size-icon;
    }
  }

  &-opened &-arrow-icon {
    transform: rotateZ(180deg);
  }

  &-expand-icon {
    transform: rotate(-45deg);
  }

  &-clear-btn {
    display: none;
    cursor: pointer;
  }

  &:hover &-clear-btn {
    display: block;

    & ~ * {
      display: none;
    }
  }
}
