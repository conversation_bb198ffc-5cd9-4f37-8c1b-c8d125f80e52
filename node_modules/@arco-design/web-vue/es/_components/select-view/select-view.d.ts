import type { PropType } from 'vue';
import type { Data } from '../../_utils/types';
import { SelectViewValue } from './interface';
declare const _default: import("vue").DefineComponent<{
    modelValue: {
        type: PropType<SelectViewValue[]>;
        required: true;
    };
    inputValue: StringConstructor;
    placeholder: StringConstructor;
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    error: {
        type: BooleanConstructor;
        default: boolean;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
    opened: {
        type: BooleanConstructor;
        default: boolean;
    };
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    bordered: {
        type: BooleanConstructor;
        default: boolean;
    };
    multiple: {
        type: BooleanConstructor;
        default: boolean;
    };
    allowClear: {
        type: BooleanConstructor;
        default: boolean;
    };
    allowCreate: {
        type: BooleanConstructor;
        default: boolean;
    };
    allowSearch: {
        type: BooleanConstructor;
        default: (props: Data) => boolean;
    };
    maxTagCount: {
        type: NumberConstructor;
        default: number;
    };
    tagNowrap: {
        type: BooleanConstructor;
        default: boolean;
    };
    retainInputValue: {
        type: BooleanConstructor;
        default: boolean;
    };
}, {
    inputRef: import("vue").ComputedRef<HTMLInputElement>;
    handleFocus: (ev: FocusEvent) => void;
    handleBlur: (ev: FocusEvent) => void;
    render: () => JSX.Element;
}, unknown, {}, {
    focus(): void;
    blur(): void;
}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("focus" | "clear" | "blur" | "remove")[], "focus" | "clear" | "blur" | "remove", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    modelValue?: unknown;
    inputValue?: unknown;
    placeholder?: unknown;
    disabled?: unknown;
    error?: unknown;
    loading?: unknown;
    opened?: unknown;
    size?: unknown;
    bordered?: unknown;
    multiple?: unknown;
    allowClear?: unknown;
    allowCreate?: unknown;
    allowSearch?: unknown;
    maxTagCount?: unknown;
    tagNowrap?: unknown;
    retainInputValue?: unknown;
} & {
    disabled: boolean;
    multiple: boolean;
    error: boolean;
    allowClear: boolean;
    modelValue: SelectViewValue[];
    retainInputValue: boolean;
    loading: boolean;
    opened: boolean;
    bordered: boolean;
    allowCreate: boolean;
    allowSearch: boolean;
    maxTagCount: number;
    tagNowrap: boolean;
} & {
    size?: "mini" | "medium" | "large" | "small" | undefined;
    placeholder?: string | undefined;
    inputValue?: string | undefined;
}> & {
    onFocus?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onClear?: ((...args: any[]) => any) | undefined;
    onRemove?: ((...args: any[]) => any) | undefined;
}, {
    disabled: boolean;
    multiple: boolean;
    error: boolean;
    allowClear: boolean;
    retainInputValue: boolean;
    loading: boolean;
    opened: boolean;
    bordered: boolean;
    allowCreate: boolean;
    allowSearch: boolean;
    maxTagCount: number;
    tagNowrap: boolean;
}>;
export default _default;
