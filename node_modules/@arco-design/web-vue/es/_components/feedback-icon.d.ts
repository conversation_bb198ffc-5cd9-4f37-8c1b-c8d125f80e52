declare const _default: import("vue").DefineComponent<{
    type: {
        type: StringConstructor;
    };
}, {
    cls: import("vue").ComputedRef<string[]>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    type?: unknown;
} & {} & {
    type?: string | undefined;
}>, {}>;
export default _default;
