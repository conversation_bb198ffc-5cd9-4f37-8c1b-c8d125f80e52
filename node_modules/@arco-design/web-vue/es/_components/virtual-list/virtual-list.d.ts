import { PropType, Ref } from 'vue';
import { ScrollOptions } from './interface';
declare const _default: import("vue").DefineComponent<{
    height: {
        type: PropType<string | number>;
        default: number;
    };
    threshold: {
        type: PropType<number | null | undefined>;
    };
    isStaticItemHeight: {
        type: BooleanConstructor;
    };
    estimatedItemHeight: {
        type: NumberConstructor;
    };
    data: {
        type: PropType<unknown[] | undefined>;
        default: () => never[];
    };
    itemKey: {
        type: PropType<string | undefined>;
        default: string;
    };
    component: {
        type: PropType<keyof HTMLElementTagNameMap | undefined>;
        default: string;
    };
    type: StringConstructor;
    outerAttrs: ObjectConstructor;
    innerAttrs: ObjectConstructor;
}, {
    viewportRef: Ref<HTMLElement | undefined>;
    viewportHeight: Ref<number>;
    totalHeight: import("vue").ComputedRef<number>;
    startOffset: Ref<number>;
    isVirtual: import("vue").ComputedRef<boolean>;
    renderChildren: () => (import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }> | undefined)[];
    handleResize: (entry: HTMLElement) => void;
    handleScroll: (e: UIEvent) => void;
    scrollTo: (options: ScrollOptions) => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("resize" | "scroll")[], "resize" | "scroll", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    height?: unknown;
    threshold?: unknown;
    isStaticItemHeight?: unknown;
    estimatedItemHeight?: unknown;
    data?: unknown;
    itemKey?: unknown;
    component?: unknown;
    type?: unknown;
    outerAttrs?: unknown;
    innerAttrs?: unknown;
} & {
    data: unknown[] | undefined;
    height: string | number;
    itemKey: string | undefined;
    component: keyof HTMLElementTagNameMap | undefined;
    isStaticItemHeight: boolean;
} & {
    type?: string | undefined;
    threshold?: number | null | undefined;
    estimatedItemHeight?: number | undefined;
    outerAttrs?: Record<string, any> | undefined;
    innerAttrs?: Record<string, any> | undefined;
}> & {
    onResize?: ((...args: any[]) => any) | undefined;
    onScroll?: ((...args: any[]) => any) | undefined;
}, {
    data: unknown[] | undefined;
    height: string | number;
    itemKey: string | undefined;
    component: keyof HTMLElementTagNameMap | undefined;
    isStaticItemHeight: boolean;
}>;
export default _default;
