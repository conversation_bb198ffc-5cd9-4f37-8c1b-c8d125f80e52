import { PropType } from 'vue';
import { SelectViewValue } from '../select-view/interface';
declare const _default: import("vue").DefineComponent<{
    modelValue: PropType<SelectViewValue>;
    inputValue: {
        type: StringConstructor;
        default: string;
    };
    enabledInput: BooleanConstructor;
    formatLabel: PropType<(data?: SelectViewValue | undefined) => string>;
    placeholder: StringConstructor;
    retainInputValue: BooleanConstructor;
    disabled: BooleanConstructor;
    baseCls: StringConstructor;
    size: PropType<"mini" | "medium" | "large" | "small">;
    error: BooleanConstructor;
    focused: BooleanConstructor;
    uninjectFormItemContext: BooleanConstructor;
}, {
    inputRef: import("vue").Ref<HTMLInputElement | undefined>;
    render: () => JSX.Element;
}, unknown, {}, {
    focus(): void;
    blur(): void;
}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("focus" | "blur" | "update:inputValue" | "inputValueChange")[], "focus" | "blur" | "update:inputValue" | "inputValueChange", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    modelValue?: unknown;
    inputValue?: unknown;
    enabledInput?: unknown;
    formatLabel?: unknown;
    placeholder?: unknown;
    retainInputValue?: unknown;
    disabled?: unknown;
    baseCls?: unknown;
    size?: unknown;
    error?: unknown;
    focused?: unknown;
    uninjectFormItemContext?: unknown;
} & {
    disabled: boolean;
    focused: boolean;
    error: boolean;
    inputValue: string;
    enabledInput: boolean;
    retainInputValue: boolean;
    uninjectFormItemContext: boolean;
} & {
    size?: "mini" | "medium" | "large" | "small" | undefined;
    placeholder?: string | undefined;
    modelValue?: SelectViewValue | undefined;
    formatLabel?: ((data?: SelectViewValue | undefined) => string) | undefined;
    baseCls?: string | undefined;
}> & {
    onFocus?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    "onUpdate:inputValue"?: ((...args: any[]) => any) | undefined;
    onInputValueChange?: ((...args: any[]) => any) | undefined;
}, {
    disabled: boolean;
    focused: boolean;
    error: boolean;
    inputValue: string;
    enabledInput: boolean;
    retainInputValue: boolean;
    uninjectFormItemContext: boolean;
}>;
export default _default;
