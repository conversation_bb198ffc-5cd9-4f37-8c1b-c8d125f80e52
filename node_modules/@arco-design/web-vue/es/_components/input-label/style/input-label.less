@import '../../../input/style/token.less';
@import '../../../input/style/input.less';

.input-label-style(@cls) {
  &.@{cls}-search {
    cursor: text;

    & .@{cls}-value {
      pointer-events: none;
    }
  }

  .input-wrapper-style(@cls);

  cursor: pointer;

  .hide-input-element() {
    // A div with width: 0 can also cause line breaks to cause the input box to be stretched, and to separate it from the standard document flow
    position: absolute;
    // Need to hide the input while ensuring that it can be focused by the Tab key
    // So don't use display: none / visibility: hidden
    width: 0 !important;
  }

  .@{cls}-input {
    .input-style();

    &-hidden {
      .hide-input-element();
    }
  }

  .@{cls}-value {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    .text-ellipsis();

    // Fix the style problem that the height of the outer div of the select layer exceeds 4px when the value is an empty string.
    &::after {
      font-size: 0;
      line-height: 0;
      visibility: hidden;
      content: '.';
    }

    &-hidden {
      display: none;
    }
  }

  .size(@size) {
    @height: ~'input-size-@{size}-height';

    &.@{cls}-size-@{size} .@{cls}-input,
    &.@{cls}-size-@{size} .@{cls}-value {
      .input-size(@size);
    }

    &.@{cls}-size-@{size} .@{cls}-value {
      min-height: round(@@height - @input-border-width * 2);
    }
  }

  .size(mini);
  .size(medium);
  .size(small);
  .size(large);
}
