import { Dayjs } from 'dayjs';
import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<{
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    focused: {
        type: BooleanConstructor;
    };
    disabled: {
        type: BooleanConstructor;
    };
    readonly: {
        type: BooleanConstructor;
    };
    error: {
        type: BooleanConstructor;
    };
    allowClear: {
        type: BooleanConstructor;
    };
    placeholder: {
        type: StringConstructor;
    };
    inputValue: {
        type: StringConstructor;
    };
    value: {
        type: PropType<Dayjs>;
    };
    format: {
        type: PropType<string | ((value: Dayjs) => string)>;
        required: true;
    };
}, {
    feedback: import("vue").Ref<string | undefined>;
    prefixCls: string;
    classNames: import("vue").ComputedRef<(string | {
        [x: string]: boolean | import("vue").Slot | undefined;
    })[]>;
    displayValue: import("vue").ComputedRef<string | undefined>;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    refInput: import("vue").Ref<HTMLInputElement | undefined>;
    onPressEnter(): void;
    onChange(e: Event): void;
    onClear(e: Event): void;
    onBlur(e: Event): void;
}, unknown, {}, {
    focus(): void;
    blur(): void;
}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("clear" | "press-enter" | "change" | "blur")[], "clear" | "press-enter" | "change" | "blur", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    size?: unknown;
    focused?: unknown;
    disabled?: unknown;
    readonly?: unknown;
    error?: unknown;
    allowClear?: unknown;
    placeholder?: unknown;
    inputValue?: unknown;
    value?: unknown;
    format?: unknown;
} & {
    disabled: boolean;
    focused: boolean;
    readonly: boolean;
    error: boolean;
    allowClear: boolean;
    format: string | ((value: Dayjs) => string);
} & {
    size?: "mini" | "medium" | "large" | "small" | undefined;
    placeholder?: string | undefined;
    inputValue?: string | undefined;
    value?: Dayjs | undefined;
}> & {
    onBlur?: ((...args: any[]) => any) | undefined;
    onChange?: ((...args: any[]) => any) | undefined;
    onClear?: ((...args: any[]) => any) | undefined;
    "onPress-enter"?: ((...args: any[]) => any) | undefined;
}, {
    disabled: boolean;
    focused: boolean;
    readonly: boolean;
    error: boolean;
    allowClear: boolean;
}>;
export default _default;
