import { Dayjs } from 'dayjs';
import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<{
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    focused: {
        type: BooleanConstructor;
    };
    focusedIndex: {
        type: NumberConstructor;
    };
    error: {
        type: BooleanConstructor;
    };
    disabled: {
        type: PropType<boolean | boolean[]>;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
    };
    allowClear: {
        type: BooleanConstructor;
    };
    placeholder: {
        type: PropType<string[]>;
        default: () => never[];
    };
    inputValue: {
        type: PropType<string[]>;
    };
    value: {
        type: PropType<(Dayjs | undefined)[]>;
        default: () => never[];
    };
    format: {
        type: PropType<string | ((value: Dayjs) => string)>;
        required: true;
    };
}, {
    prefixCls: string;
    classNames: import("vue").ComputedRef<(string | {
        [x: string]: boolean | import("vue").Slot | undefined;
    })[]>;
    refInput0: import("vue").Ref<HTMLInputElement | undefined>;
    refInput1: import("vue").Ref<HTMLInputElement | undefined>;
    disabled0: import("vue").ComputedRef<boolean>;
    disabled1: import("vue").ComputedRef<boolean>;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    getDisabled: (index: number) => boolean;
    getInputWrapClassName: (index: number) => (string | {
        [x: string]: boolean;
    })[];
    displayValue0: import("vue").ComputedRef<string | undefined>;
    displayValue1: import("vue").ComputedRef<string | undefined>;
    changeFocusedInput: (index: number) => void;
    onChange: (e: Event) => void;
    onPressEnter: () => void;
    onPressTab: (e: Event) => void;
    onClear: (e: Event) => void;
    feedback: import("vue").Ref<string | undefined>;
}, unknown, {}, {
    focus(index?: number | undefined): void;
    blur(): void;
}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("clear" | "press-enter" | "change" | "focused-index-change" | "update:focusedIndex")[], "clear" | "press-enter" | "change" | "focused-index-change" | "update:focusedIndex", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    size?: unknown;
    focused?: unknown;
    focusedIndex?: unknown;
    error?: unknown;
    disabled?: unknown;
    readonly?: unknown;
    allowClear?: unknown;
    placeholder?: unknown;
    inputValue?: unknown;
    value?: unknown;
    format?: unknown;
} & {
    disabled: boolean | boolean[];
    focused: boolean;
    readonly: boolean;
    error: boolean;
    allowClear: boolean;
    placeholder: string[];
    value: (Dayjs | undefined)[];
    format: string | ((value: Dayjs) => string);
} & {
    size?: "mini" | "medium" | "large" | "small" | undefined;
    inputValue?: string[] | undefined;
    focusedIndex?: number | undefined;
}> & {
    onChange?: ((...args: any[]) => any) | undefined;
    onClear?: ((...args: any[]) => any) | undefined;
    "onPress-enter"?: ((...args: any[]) => any) | undefined;
    "onFocused-index-change"?: ((...args: any[]) => any) | undefined;
    "onUpdate:focusedIndex"?: ((...args: any[]) => any) | undefined;
}, {
    disabled: boolean | boolean[];
    focused: boolean;
    readonly: boolean;
    error: boolean;
    allowClear: boolean;
    placeholder: string[];
    value: (Dayjs | undefined)[];
}>;
export default _default;
