/******** 基础配置项 end *******/
/******** 基础配置项 end *******/
/******** 高级配置项 *******/
/******** 高级配置项 end *******/
/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
.arco-picker {
  position: relative;
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
  padding: 4px 11px 4px 4px;
  line-height: 1.5715;
  background-color: var(--color-fill-2);
  border: 1px solid transparent;
  border-radius: var(--border-radius-small);
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-picker-input {
  display: inline-flex;
  flex: 1;
}
.arco-picker input {
  width: 100%;
  padding: 0;
  padding-left: 8px;
  color: var(--color-text-2);
  line-height: 1.5715;
  text-align: left;
  background-color: transparent;
  border: none;
  outline: none;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-picker input::placeholder {
  color: var(--color-text-3);
}
.arco-picker input[disabled] {
  -webkit-text-fill-color: var(--color-text-4);
}
.arco-picker-has-prefix {
  padding-left: 12px;
}
.arco-picker-prefix {
  padding-right: 4px;
  color: var(--color-text-2);
  font-size: 14px;
}
.arco-picker-suffix {
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
}
.arco-picker-suffix .arco-feedback-icon {
  display: inline-flex;
}
.arco-picker-suffix .arco-feedback-icon-status-validating {
  color: rgb(var(--primary-6));
}
.arco-picker-suffix .arco-feedback-icon-status-success {
  color: rgb(var(--success-6));
}
.arco-picker-suffix .arco-feedback-icon-status-warning {
  color: rgb(var(--warning-6));
}
.arco-picker-suffix .arco-feedback-icon-status-error {
  color: rgb(var(--danger-6));
}
.arco-picker-suffix .arco-feedback-icon {
  margin-left: 4px;
}
.arco-picker-suffix-icon {
  color: var(--color-text-2);
}
.arco-picker .arco-picker-clear-icon {
  display: none;
  color: var(--color-text-2);
  font-size: 12px;
}
.arco-picker:hover {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-picker:not(.arco-picker-disabled):hover .arco-picker-clear-icon {
  display: inline-block;
}
.arco-picker:not(.arco-picker-disabled):hover .arco-picker-suffix .arco-picker-clear-icon + span {
  display: none;
}
.arco-picker input[disabled] {
  color: var(--color-text-4);
  cursor: not-allowed;
}
.arco-picker input[disabled]::placeholder {
  color: var(--color-text-4);
}
.arco-picker-error {
  background-color: var(--color-danger-light-1);
  border-color: transparent;
}
.arco-picker-error:hover {
  background-color: var(--color-danger-light-2);
  border-color: transparent;
}
.arco-picker-focused {
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-picker-focused,
.arco-picker-focused:hover {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--primary-6));
}
.arco-picker-focused.arco-picker-error {
  border-color: rgb(var(--danger-6));
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-picker-focused .arco-picker-input-active input,
.arco-picker-focused:hover .arco-picker-input-active input {
  background: var(--color-fill-2);
}
.arco-picker-disabled,
.arco-picker-disabled:hover {
  color: var(--color-text-4);
  background-color: var(--color-fill-2);
  border-color: transparent;
  cursor: not-allowed;
}
.arco-picker-disabled input[disabled],
.arco-picker-disabled:hover input[disabled] {
  color: var(--color-text-4);
  cursor: not-allowed;
}
.arco-picker-disabled input[disabled]::placeholder,
.arco-picker-disabled:hover input[disabled]::placeholder {
  color: var(--color-text-4);
}
.arco-picker-separator {
  min-width: 10px;
  padding: 0 8px;
  color: var(--color-text-3);
}
.arco-picker-disabled .arco-picker-separator {
  color: var(--color-text-4);
}
.arco-picker-disabled .arco-picker-suffix-icon {
  color: var(--color-text-4);
}
.arco-picker-size-mini {
  height: 24px;
}
.arco-picker-size-mini input {
  font-size: 12px;
}
.arco-picker-size-small {
  height: 28px;
}
.arco-picker-size-small input {
  font-size: 14px;
}
.arco-picker-size-medium {
  height: 32px;
}
.arco-picker-size-medium input {
  font-size: 14px;
}
.arco-picker-size-large {
  height: 36px;
}
.arco-picker-size-large input {
  font-size: 14px;
}
