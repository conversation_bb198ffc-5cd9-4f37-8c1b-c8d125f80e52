declare const _default: import("vue").DefineComponent<{
    tooltipProps: {
        type: ObjectConstructor;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    tooltipProps?: unknown;
} & {} & {
    tooltipProps?: Record<string, any> | undefined;
}>, {}>;
export default _default;
