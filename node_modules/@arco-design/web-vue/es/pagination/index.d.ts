import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _Pagination from './pagination';
declare const Pagination: {
    new (...args: any[]): {
        $: import("vue").ComponentInternalInstance;
        $data: {};
        $props: Partial<{
            disabled: boolean;
            defaultCurrent: number;
            simple: boolean;
            defaultPageSize: number;
            hideOnSinglePage: boolean;
            showTotal: boolean;
            showMore: boolean;
            showJumper: boolean;
            showPageSize: boolean;
            pageSizeOptions: number[];
            baseSize: number;
            bufferSize: number;
            autoAdjust: boolean;
        }> & Omit<Readonly<{
            total?: unknown;
            current?: unknown;
            defaultCurrent?: unknown;
            pageSize?: unknown;
            defaultPageSize?: unknown;
            disabled?: unknown;
            hideOnSinglePage?: unknown;
            simple?: unknown;
            showTotal?: unknown;
            showMore?: unknown;
            showJumper?: unknown;
            showPageSize?: unknown;
            pageSizeOptions?: unknown;
            pageSizeProps?: unknown;
            size?: unknown;
            pageItemStyle?: unknown;
            activePageItemStyle?: unknown;
            baseSize?: unknown;
            bufferSize?: unknown;
            autoAdjust?: unknown;
        } & {
            disabled: boolean;
            defaultCurrent: number;
            total: number;
            simple: boolean;
            defaultPageSize: number;
            hideOnSinglePage: boolean;
            showTotal: boolean;
            showMore: boolean;
            showJumper: boolean;
            showPageSize: boolean;
            pageSizeOptions: number[];
            baseSize: number;
            bufferSize: number;
            autoAdjust: boolean;
        } & {
            size?: "mini" | "medium" | "large" | "small" | undefined;
            current?: number | undefined;
            pageSize?: number | undefined;
            pageSizeProps?: import("..").SelectProps | undefined;
            pageItemStyle?: import("vue").CSSProperties | undefined;
            activePageItemStyle?: import("vue").CSSProperties | undefined;
        }> & {
            onChange?: ((current: number) => any) | undefined;
            "onUpdate:current"?: ((current: number) => any) | undefined;
            onPageSizeChange?: ((pageSize: number) => any) | undefined;
            "onUpdate:pageSize"?: ((pageSize: number) => any) | undefined;
        } & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, "disabled" | "defaultCurrent" | "simple" | "defaultPageSize" | "hideOnSinglePage" | "showTotal" | "showMore" | "showJumper" | "showPageSize" | "pageSizeOptions" | "baseSize" | "bufferSize" | "autoAdjust">;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        };
        $slots: Readonly<{
            [name: string]: import("vue").Slot | undefined;
        }>;
        $root: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $parent: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $emit: ((event: "change", current: number) => void) & ((event: "pageSizeChange", pageSize: number) => void) & ((event: "update:current", current: number) => void) & ((event: "update:pageSize", pageSize: number) => void);
        $el: any;
        $options: import("vue").ComponentOptionsBase<Readonly<{
            total?: unknown;
            current?: unknown;
            defaultCurrent?: unknown;
            pageSize?: unknown;
            defaultPageSize?: unknown;
            disabled?: unknown;
            hideOnSinglePage?: unknown;
            simple?: unknown;
            showTotal?: unknown;
            showMore?: unknown;
            showJumper?: unknown;
            showPageSize?: unknown;
            pageSizeOptions?: unknown;
            pageSizeProps?: unknown;
            size?: unknown;
            pageItemStyle?: unknown;
            activePageItemStyle?: unknown;
            baseSize?: unknown;
            bufferSize?: unknown;
            autoAdjust?: unknown;
        } & {
            disabled: boolean;
            defaultCurrent: number;
            total: number;
            simple: boolean;
            defaultPageSize: number;
            hideOnSinglePage: boolean;
            showTotal: boolean;
            showMore: boolean;
            showJumper: boolean;
            showPageSize: boolean;
            pageSizeOptions: number[];
            baseSize: number;
            bufferSize: number;
            autoAdjust: boolean;
        } & {
            size?: "mini" | "medium" | "large" | "small" | undefined;
            current?: number | undefined;
            pageSize?: number | undefined;
            pageSizeProps?: import("..").SelectProps | undefined;
            pageItemStyle?: import("vue").CSSProperties | undefined;
            activePageItemStyle?: import("vue").CSSProperties | undefined;
        }> & {
            onChange?: ((current: number) => any) | undefined;
            "onUpdate:current"?: ((current: number) => any) | undefined;
            onPageSizeChange?: ((pageSize: number) => any) | undefined;
            "onUpdate:pageSize"?: ((pageSize: number) => any) | undefined;
        }, () => JSX.Element | null, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            'update:current': (current: number) => true;
            'update:pageSize': (pageSize: number) => true;
            change: (current: number) => true;
            pageSizeChange: (pageSize: number) => true;
        }, string, {
            disabled: boolean;
            defaultCurrent: number;
            simple: boolean;
            defaultPageSize: number;
            hideOnSinglePage: boolean;
            showTotal: boolean;
            showMore: boolean;
            showJumper: boolean;
            showPageSize: boolean;
            pageSizeOptions: number[];
            baseSize: number;
            bufferSize: number;
            autoAdjust: boolean;
        }> & {
            beforeCreate?: ((() => void) | (() => void)[]) | undefined;
            created?: ((() => void) | (() => void)[]) | undefined;
            beforeMount?: ((() => void) | (() => void)[]) | undefined;
            mounted?: ((() => void) | (() => void)[]) | undefined;
            beforeUpdate?: ((() => void) | (() => void)[]) | undefined;
            updated?: ((() => void) | (() => void)[]) | undefined;
            activated?: ((() => void) | (() => void)[]) | undefined;
            deactivated?: ((() => void) | (() => void)[]) | undefined;
            beforeDestroy?: ((() => void) | (() => void)[]) | undefined;
            beforeUnmount?: ((() => void) | (() => void)[]) | undefined;
            destroyed?: ((() => void) | (() => void)[]) | undefined;
            unmounted?: ((() => void) | (() => void)[]) | undefined;
            renderTracked?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            renderTriggered?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            errorCaptured?: (((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void) | ((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void)[]) | undefined;
        };
        $forceUpdate: () => void;
        $nextTick: typeof import("vue").nextTick;
        $watch(source: string | Function, cb: Function, options?: import("vue").WatchOptions<boolean> | undefined): import("vue").WatchStopHandle;
    } & Readonly<{
        total?: unknown;
        current?: unknown;
        defaultCurrent?: unknown;
        pageSize?: unknown;
        defaultPageSize?: unknown;
        disabled?: unknown;
        hideOnSinglePage?: unknown;
        simple?: unknown;
        showTotal?: unknown;
        showMore?: unknown;
        showJumper?: unknown;
        showPageSize?: unknown;
        pageSizeOptions?: unknown;
        pageSizeProps?: unknown;
        size?: unknown;
        pageItemStyle?: unknown;
        activePageItemStyle?: unknown;
        baseSize?: unknown;
        bufferSize?: unknown;
        autoAdjust?: unknown;
    } & {
        disabled: boolean;
        defaultCurrent: number;
        total: number;
        simple: boolean;
        defaultPageSize: number;
        hideOnSinglePage: boolean;
        showTotal: boolean;
        showMore: boolean;
        showJumper: boolean;
        showPageSize: boolean;
        pageSizeOptions: number[];
        baseSize: number;
        bufferSize: number;
        autoAdjust: boolean;
    } & {
        size?: "mini" | "medium" | "large" | "small" | undefined;
        current?: number | undefined;
        pageSize?: number | undefined;
        pageSizeProps?: import("..").SelectProps | undefined;
        pageItemStyle?: import("vue").CSSProperties | undefined;
        activePageItemStyle?: import("vue").CSSProperties | undefined;
    }> & {
        onChange?: ((current: number) => any) | undefined;
        "onUpdate:current"?: ((current: number) => any) | undefined;
        onPageSizeChange?: ((pageSize: number) => any) | undefined;
        "onUpdate:pageSize"?: ((pageSize: number) => any) | undefined;
    } & import("vue").ShallowUnwrapRef<() => JSX.Element | null> & {} & {} & import("vue").ComponentCustomProperties;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<{
    total?: unknown;
    current?: unknown;
    defaultCurrent?: unknown;
    pageSize?: unknown;
    defaultPageSize?: unknown;
    disabled?: unknown;
    hideOnSinglePage?: unknown;
    simple?: unknown;
    showTotal?: unknown;
    showMore?: unknown;
    showJumper?: unknown;
    showPageSize?: unknown;
    pageSizeOptions?: unknown;
    pageSizeProps?: unknown;
    size?: unknown;
    pageItemStyle?: unknown;
    activePageItemStyle?: unknown;
    baseSize?: unknown;
    bufferSize?: unknown;
    autoAdjust?: unknown;
} & {
    disabled: boolean;
    defaultCurrent: number;
    total: number;
    simple: boolean;
    defaultPageSize: number;
    hideOnSinglePage: boolean;
    showTotal: boolean;
    showMore: boolean;
    showJumper: boolean;
    showPageSize: boolean;
    pageSizeOptions: number[];
    baseSize: number;
    bufferSize: number;
    autoAdjust: boolean;
} & {
    size?: "mini" | "medium" | "large" | "small" | undefined;
    current?: number | undefined;
    pageSize?: number | undefined;
    pageSizeProps?: import("..").SelectProps | undefined;
    pageItemStyle?: import("vue").CSSProperties | undefined;
    activePageItemStyle?: import("vue").CSSProperties | undefined;
}> & {
    onChange?: ((current: number) => any) | undefined;
    "onUpdate:current"?: ((current: number) => any) | undefined;
    onPageSizeChange?: ((pageSize: number) => any) | undefined;
    "onUpdate:pageSize"?: ((pageSize: number) => any) | undefined;
}, () => JSX.Element | null, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:current': (current: number) => true;
    'update:pageSize': (pageSize: number) => true;
    change: (current: number) => true;
    pageSizeChange: (pageSize: number) => true;
}, string, {
    disabled: boolean;
    defaultCurrent: number;
    simple: boolean;
    defaultPageSize: number;
    hideOnSinglePage: boolean;
    showTotal: boolean;
    showMore: boolean;
    showJumper: boolean;
    showPageSize: boolean;
    pageSizeOptions: number[];
    baseSize: number;
    bufferSize: number;
    autoAdjust: boolean;
}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type PaginationInstance = InstanceType<typeof _Pagination>;
export type { PaginationProps } from './interface';
export default Pagination;
