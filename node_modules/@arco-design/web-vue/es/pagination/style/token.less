@import '../../style/theme/index.less';

@pagination-prefix-cls: ~'@{prefix}-pagination';

@pagination-item-border-radius: @radius-small;
@pagination-item-spacing: @spacing-4;
@pagination-margin-total-spacing: @spacing-4;
@pagination-margin-option-left: @spacing-4;
@pagination-margin-jumper-left: @spacing-4;

@pagination-size-mini: @size-mini;
@pagination-size-small: @size-small;
@pagination-size-default: @size-default;
@pagination-size-large: @size-large;
@pagination-padding-horizontal: 8px;

@pagination-size-mini-font-size: @font-size-body-1;
@pagination-size-small-font-size: @font-size-body-3;
@pagination-size-default-font-size: @font-size-body-3;
@pagination-size-large-font-size: @font-size-body-3;

@pagination-size-icon-arrow_mini: @size-3;
@pagination-size-icon-arrow_small: @size-3;
@pagination-size-icon-arrow_default: @size-3;
@pagination-size-icon-arrow_large: 14px;

@pagination-size-icon-ellipsis: @size-4;

@pagination-border-width: @border-none;
@pagination-color-bg-item: @color-transparent;
@pagination-color-bg-item_active: var(
  ~'@{arco-cssvars-prefix}-color-primary-light-1'
);
@pagination-color-bg-item_hover: var(~'@{arco-cssvars-prefix}-color-fill-1');
@pagination-color-bg-item_disabled: @color-transparent;
@pagination-color-bg-item_active_disabled: var(
  ~'@{arco-cssvars-prefix}-color-fill-1'
);

@pagination-color-item-text: var(~'@{arco-cssvars-prefix}-color-text-2');
@pagination-color-item-text_hover: var(~'@{arco-cssvars-prefix}-color-text-2');
@pagination-color-item-text_active: @color-primary-6;
@pagination-color-item-text_disabled: var(
  ~'@{arco-cssvars-prefix}-color-text-4'
);
@pagination-color-item-text_active_disabled: var(
  ~'@{arco-cssvars-prefix}-color-primary-light-3'
);

@pagination-color-item-border: @color-transparent;
@pagination-color-item-border_active: @color-transparent;
@pagination-color-item-border_hover: @color-transparent;
@pagination-color-item-border_disabled: @color-transparent;
@pagination-color-item-border_active_disabled: @color-transparent;

@pagination-color-icon-arrow: var(~'@{arco-cssvars-prefix}-color-text-2');
@pagination-color-icon-arrow-bg: @color-transparent;
@pagination-color-icon-arrow-bg_hover: var(
  ~'@{arco-cssvars-prefix}-color-fill-1'
);
@pagination-color-icon-arrow-bg_disabled: @color-transparent;
@pagination-color-icon-arrow-text_hover: @color-primary-6;
@pagination-color-icon-arrow-text_disabled: var(
  ~'@{arco-cssvars-prefix}-color-text-4'
);

// Simple
@pagination-simple-input-width: @size-10;
@pagination-simple-color-icon-arrow: var(
  ~'@{arco-cssvars-prefix}-color-text-2'
);
@pagination-simple-color-icon-arrow-bg: @color-transparent;
@pagination-simple-color-icon-arrow-bg_hover: var(
  ~'@{arco-cssvars-prefix}-color-fill-1'
);
@pagination-simple-color-icon-arrow-bg_disabled: @color-transparent;
@pagination-simple-color-icon-arrow-text_hover: @color-primary-6;
@pagination-simple-color-icon-arrow-text_disabled: var(
  ~'@{arco-cssvars-prefix}-color-text-4'
);

@pagination-simple-margin-prev-right: @spacing-2;
@pagination-simple-margin-next-left: @spacing-6;
@pagination-simple-margin-separator-left: @spacing-6;
@pagination-simple-margin-separator-right: @spacing-6;
// jumper
@patination-jumper-input-width: @size-10;
@pagination-color-jumper-goto: var(~'@{arco-cssvars-prefix}-color-text-3');

// total text
@pagination-color-text-total: var(~'@{arco-cssvars-prefix}-color-text-1');
