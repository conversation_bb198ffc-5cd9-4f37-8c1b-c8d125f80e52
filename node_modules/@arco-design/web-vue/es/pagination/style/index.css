/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
.arco-pagination {
  display: flex;
  align-items: center;
  font-size: 14px;
  /*** 快速跳转 ***/
}
.arco-pagination-list {
  display: inline-block;
  margin: 0;
  padding: 0;
  white-space: nowrap;
  list-style: none;
}
.arco-pagination-item {
  display: inline-block;
  box-sizing: border-box;
  padding: 0 8px;
  color: var(--color-text-2);
  text-align: center;
  vertical-align: middle;
  list-style: none;
  background-color: transparent;
  border: 0 solid transparent;
  border-radius: var(--border-radius-small);
  outline: 0;
  cursor: pointer;
  user-select: none;
  min-width: 32px;
  height: 32px;
  font-size: 14px;
  line-height: 32px;
}
.arco-pagination-item-previous,
.arco-pagination-item-next {
  font-size: 12px;
}
.arco-pagination-item:hover {
  color: var(--color-text-2);
  background-color: var(--color-fill-1);
  border-color: transparent;
}
.arco-pagination-item-active,
.arco-pagination-item-active:hover {
  color: rgb(var(--primary-6));
  background-color: var(--color-primary-light-1);
  border-color: transparent;
  transition: color 0.2s cubic-bezier(0, 0, 1, 1), background-color 0.2s cubic-bezier(0, 0, 1, 1);
}
.arco-pagination-item-disabled,
.arco-pagination-item-disabled:hover {
  color: var(--color-text-4);
  background-color: transparent;
  border-color: transparent;
  cursor: not-allowed;
}
.arco-pagination-item:not(:last-child) {
  margin-right: 8px;
}
.arco-pagination-item-previous,
.arco-pagination-item-next {
  color: var(--color-text-2);
  font-size: 12px;
  background-color: transparent;
}
.arco-pagination-item-previous:not(.arco-pagination-item-disabled):hover,
.arco-pagination-item-next:not(.arco-pagination-item-disabled):hover {
  color: rgb(var(--primary-6));
  background-color: var(--color-fill-1);
}
.arco-pagination-item-previous::after,
.arco-pagination-item-next::after {
  display: inline-block;
  font-size: 0;
  vertical-align: middle;
  content: '.';
}
.arco-pagination .arco-pagination-item-previous.arco-pagination-item-disabled,
.arco-pagination .arco-pagination-item-next.arco-pagination-item-disabled {
  color: var(--color-text-4);
  background-color: transparent;
}
.arco-pagination-item-jumper {
  font-size: 16px;
}
.arco-pagination-jumper {
  display: flex;
  align-items: center;
  margin-left: 8px;
}
.arco-pagination-jumper > span {
  font-size: 14px;
}
.arco-pagination-jumper-text-goto,
.arco-pagination-jumper-prepend,
.arco-pagination-jumper-append {
  color: var(--color-text-3);
  white-space: nowrap;
}
.arco-pagination-jumper-prepend {
  margin-right: 8px;
}
.arco-pagination-jumper-append {
  margin-left: 8px;
}
.arco-pagination-jumper .arco-pagination-jumper-input {
  width: 40px;
  padding-right: 2px;
  padding-left: 2px;
}
.arco-pagination-jumper .arco-pagination-jumper-input input {
  text-align: center;
}
.arco-pagination-options {
  position: relative;
  display: inline-block;
  flex: 0 0 auto;
  min-width: 0;
  margin-left: 8px;
  text-align: center;
  vertical-align: middle;
}
.arco-pagination-options .arco-select {
  width: auto;
}
.arco-pagination-options .arco-select-view-value {
  padding-right: 6px;
  overflow: inherit;
}
.arco-pagination-total {
  display: inline-block;
  height: 100%;
  margin-right: 8px;
  color: var(--color-text-1);
  font-size: 14px;
  line-height: 32px;
  white-space: nowrap;
}
.arco-pagination-jumper {
  flex: 0 0 auto;
}
.arco-pagination-jumper-separator {
  padding: 0 12px 0 12px;
}
.arco-pagination-jumper-total-page {
  margin-right: 8px;
}
.arco-pagination-simple {
  display: flex;
  align-items: center;
}
.arco-pagination-simple .arco-pagination-item {
  margin-right: 0;
}
.arco-pagination-simple .arco-pagination-jumper {
  margin: 0 4px;
  color: var(--color-text-1);
}
.arco-pagination-simple .arco-pagination-jumper .arco-pagination-jumper-input {
  width: 40px;
  margin-left: 0;
}
.arco-pagination-simple .arco-pagination-item-previous,
.arco-pagination-simple .arco-pagination-item-next {
  color: var(--color-text-2);
  background-color: transparent;
}
.arco-pagination-simple .arco-pagination-item-previous:not(.arco-pagination-item-disabled):hover,
.arco-pagination-simple .arco-pagination-item-next:not(.arco-pagination-item-disabled):hover {
  color: rgb(var(--primary-6));
  background-color: var(--color-fill-1);
}
.arco-pagination-simple .arco-pagination-item-previous.arco-pagination-item-disabled,
.arco-pagination-simple .arco-pagination-item-next.arco-pagination-item-disabled {
  color: var(--color-text-4);
  background-color: transparent;
}
.arco-pagination-disabled {
  cursor: not-allowed;
}
.arco-pagination-disabled .arco-pagination-item,
.arco-pagination-disabled .arco-pagination-item:not(.arco-pagination-item-disabled):not(.arco-pagination-item-active):hover {
  color: var(--color-text-4);
  background-color: transparent;
  border-color: transparent;
  cursor: not-allowed;
}
.arco-pagination.arco-pagination-disabled .arco-pagination-item-active {
  color: var(--color-primary-light-3);
  background-color: var(--color-fill-1);
  border-color: transparent;
}
.arco-pagination-size-mini .arco-pagination-item {
  min-width: 24px;
  height: 24px;
  font-size: 12px;
  line-height: 24px;
}
.arco-pagination-size-mini .arco-pagination-item-previous,
.arco-pagination-size-mini .arco-pagination-item-next {
  font-size: 12px;
}
.arco-pagination-size-mini .arco-pagination-total {
  font-size: 12px;
  line-height: 24px;
}
.arco-pagination-size-mini .arco-pagination-option {
  height: 24px;
  font-size: 12px;
  line-height: 0;
}
.arco-pagination-size-mini .arco-pagination-jumper > span {
  font-size: 12px;
}
.arco-pagination-size-small .arco-pagination-item {
  min-width: 28px;
  height: 28px;
  font-size: 14px;
  line-height: 28px;
}
.arco-pagination-size-small .arco-pagination-item-previous,
.arco-pagination-size-small .arco-pagination-item-next {
  font-size: 12px;
}
.arco-pagination-size-small .arco-pagination-total {
  font-size: 14px;
  line-height: 28px;
}
.arco-pagination-size-small .arco-pagination-option {
  height: 28px;
  font-size: 14px;
  line-height: 0;
}
.arco-pagination-size-small .arco-pagination-jumper > span {
  font-size: 14px;
}
.arco-pagination-size-large .arco-pagination-item {
  min-width: 36px;
  height: 36px;
  font-size: 14px;
  line-height: 36px;
}
.arco-pagination-size-large .arco-pagination-item-previous,
.arco-pagination-size-large .arco-pagination-item-next {
  font-size: 14px;
}
.arco-pagination-size-large .arco-pagination-total {
  font-size: 14px;
  line-height: 36px;
}
.arco-pagination-size-large .arco-pagination-option {
  height: 36px;
  font-size: 14px;
  line-height: 0;
}
.arco-pagination-size-large .arco-pagination-jumper > span {
  font-size: 14px;
}
