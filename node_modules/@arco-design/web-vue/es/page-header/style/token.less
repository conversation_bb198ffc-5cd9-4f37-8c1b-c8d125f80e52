@import '../../style/theme/index.less';

@page-header-padding-left: @spacing-9;
@page-header-padding-right: @spacing-8;
@page-header-padding-vertical: @spacing-7;
@page-header-padding-vertical_breadcrumb: @spacing-6;
@page-header-color-back-icon: var(~'@{arco-cssvars-prefix}-color-text-2');
@page-header-size-back-icon: @font-size-body-3;
@page-header-margin-back-icon-right: @spacing-6;
@page-header-line-height: @size-7;
@page-header-color-title-text: var(~'@{arco-cssvars-prefix}-color-text-1');
@page-header-weight-title-text: 600;
@page-header-color-back-icon-bg_hover: var(~'@{arco-cssvars-prefix}-color-fill-2');
@page-header-size-back-icon-bg_hover: 30px;
@page-header-size-title-text: @font-size-title-2;

@page-header-color-divider-bg: var(~'@{arco-cssvars-prefix}-color-fill-3');
@page-header-size-divider-height: @size-4;
@page-header-size-divider-width: 1px;
@page-header-margin-divider-left: @spacing-6;
@page-header-margin-divider-right: @spacing-6;

@page-header-color-sub-title-text: var(~'@{arco-cssvars-prefix}-color-text-3');
@page-header-size-sub-title-text: @font-size-body-3;
@page-header-color-header-border: var(~'@{arco-cssvars-prefix}-color-neutral-3');
@page-header-border-header-width: @border-1;
@page-header-border-header-style: @border-solid;
@page-header-padding-content-vertical: @spacing-8;
@page-header-padding-content-horizontal: @spacing-10;
@page-header-margin-breadcrumb-bottom: @spacing-2;
