@import './token.less';

@tree-prefix-cls: ~'@{prefix}-tree';
@tree-select-prefix-cls: ~'@{prefix}-tree-select';

// .select-view(@tree-select-prefix-cls);

.@{tree-select-prefix-cls} {
  &-popup {
    box-sizing: border-box;
    padding: @tree-select-padding-popup-vertical 0;
    background-color: var(~'@{arco-cssvars-prefix}-color-bg-popup');
    border: 1px solid @select-popup-color-border;
    border-radius: @select-popup-border-radius;
    box-shadow: @select-popup-box-shadow;

    .@{tree-select-prefix-cls}-tree-wrapper {
      height: 100%;
      max-height: @select-popup-max-height;
      padding-right: @tree-select-padding-popup-right;
      padding-left: @tree-select-padding-popup-left;
      overflow: auto;
    }

    .@{tree-prefix-cls}-node {
      padding-left: 0;
    }
  }

  &-highlight {
    font-weight: @font-weight-500;
  }

  &-has-header {
    padding-top: 0;
  }

  &-header {
    border-bottom: 1px solid @select-popup-color-border;
  }

  &-has-footer {
    padding-bottom: 0;
  }

  &-footer {
    border-top: 1px solid @select-popup-color-border;
  }
}
