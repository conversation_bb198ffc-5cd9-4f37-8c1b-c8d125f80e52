/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
/**********************************************
 * Popup Box
 **********************************************/
/**********************************************
 * Popup Options
 * status: default / disabled / selected / hover
 **********************************************/
.arco-tree-select-popup {
  box-sizing: border-box;
  padding: 4px 0;
  background-color: var(--color-bg-popup);
  border: 1px solid var(--color-fill-3);
  border-radius: var(--border-radius-medium);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
.arco-tree-select-popup .arco-tree-select-tree-wrapper {
  height: 100%;
  max-height: 200px;
  padding-right: 4px;
  padding-left: 10px;
  overflow: auto;
}
.arco-tree-select-popup .arco-tree-node {
  padding-left: 0;
}
.arco-tree-select-highlight {
  font-weight: 500;
}
.arco-tree-select-has-header {
  padding-top: 0;
}
.arco-tree-select-header {
  border-bottom: 1px solid var(--color-fill-3);
}
.arco-tree-select-has-footer {
  padding-bottom: 0;
}
.arco-tree-select-footer {
  border-top: 1px solid var(--color-fill-3);
}
