import { App } from 'vue';
import { ArcoOptions } from '../_utils/types';
import _TreeSelect from './tree-select';
declare const TreeSelect: {
    new (...args: any[]): {
        $: import("vue").ComponentInternalInstance;
        $data: {};
        $props: Partial<{
            popupVisible: boolean;
            defaultPopupVisible: boolean;
            disabled: boolean;
            scrollbar: boolean | import("..").ScrollbarProps;
            border: boolean;
            multiple: boolean;
            data: import("..").TreeNodeData[];
            error: boolean;
            allowClear: boolean;
            loading: boolean;
            allowSearch: boolean | {
                retainInputValue?: boolean | undefined;
            };
            defaultInputValue: string;
            fallbackOption: boolean | ((key: string | number) => boolean | import("..").TreeNodeData);
            showHeaderOnEmpty: boolean;
            showFooterOnEmpty: boolean;
            selectable: boolean | "leaf" | ((node: import("..").TreeNodeData, info: {
                isLeaf: boolean;
                level: number;
            }) => boolean);
            labelInValue: boolean;
            treeCheckable: boolean;
            treeCheckStrictly: boolean;
            treeCheckedStrategy: "all" | "child" | "parent";
            disableFilter: boolean;
        }> & Omit<Readonly<{
            disabled?: unknown;
            loading?: unknown;
            error?: unknown;
            size?: unknown;
            border?: unknown;
            allowSearch?: unknown;
            allowClear?: unknown;
            placeholder?: unknown;
            maxTagCount?: unknown;
            multiple?: unknown;
            defaultValue?: unknown;
            modelValue?: unknown;
            fieldNames?: unknown;
            data?: unknown;
            labelInValue?: unknown;
            treeCheckable?: unknown;
            treeCheckStrictly?: unknown;
            treeCheckedStrategy?: unknown;
            treeProps?: unknown;
            triggerProps?: unknown;
            popupVisible?: unknown;
            defaultPopupVisible?: unknown;
            dropdownStyle?: unknown;
            dropdownClassName?: unknown;
            filterTreeNode?: unknown;
            loadMore?: unknown;
            disableFilter?: unknown;
            popupContainer?: unknown;
            fallbackOption?: unknown;
            selectable?: unknown;
            scrollbar?: unknown;
            showHeaderOnEmpty?: unknown;
            showFooterOnEmpty?: unknown;
            inputValue?: unknown;
            defaultInputValue?: unknown;
        } & {
            defaultPopupVisible: boolean;
            disabled: boolean;
            scrollbar: boolean | import("..").ScrollbarProps;
            border: boolean;
            multiple: boolean;
            data: import("..").TreeNodeData[];
            error: boolean;
            allowClear: boolean;
            loading: boolean;
            allowSearch: boolean | {
                retainInputValue?: boolean | undefined;
            };
            defaultInputValue: string;
            fallbackOption: boolean | ((key: string | number) => boolean | import("..").TreeNodeData);
            showHeaderOnEmpty: boolean;
            showFooterOnEmpty: boolean;
            selectable: boolean | "leaf" | ((node: import("..").TreeNodeData, info: {
                isLeaf: boolean;
                level: number;
            }) => boolean);
            labelInValue: boolean;
            treeCheckable: boolean;
            treeCheckStrictly: boolean;
            treeCheckedStrategy: "all" | "child" | "parent";
            disableFilter: boolean;
        } & {
            popupVisible?: boolean | undefined;
            popupContainer?: string | HTMLElement | undefined;
            size?: "mini" | "medium" | "large" | "small" | undefined;
            placeholder?: string | undefined;
            inputValue?: string | undefined;
            defaultValue?: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined;
            modelValue?: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined;
            maxTagCount?: number | undefined;
            fieldNames?: import("..").TreeFieldNames | undefined;
            triggerProps?: Partial<import("..").TriggerProps> | undefined;
            loadMore?: ((nodeData: import("..").TreeNodeData) => Promise<void>) | undefined;
            treeProps?: Partial<import("../tree/interface").TreeProps> | undefined;
            filterTreeNode?: ((searchKey: string, nodeData: import("..").TreeNodeData) => boolean) | undefined;
            dropdownStyle?: import("vue").CSSProperties | undefined;
            dropdownClassName?: string | string[] | undefined;
        }> & {
            "onUpdate:popupVisible"?: ((visible: boolean) => any) | undefined;
            onChange?: ((value: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined) => any) | undefined;
            onClear?: (() => any) | undefined;
            "onUpdate:inputValue"?: ((inputValue: string) => any) | undefined;
            onInputValueChange?: ((inputValue: string) => any) | undefined;
            "onUpdate:modelValue"?: ((value: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined) => any) | undefined;
            "onPopup-visible-change"?: ((visible: boolean) => any) | undefined;
            onSearch?: ((searchKey: string) => any) | undefined;
        } & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, "popupVisible" | "defaultPopupVisible" | "disabled" | "scrollbar" | "border" | "multiple" | "data" | "error" | "allowClear" | "loading" | "allowSearch" | "defaultInputValue" | "fallbackOption" | "showHeaderOnEmpty" | "showFooterOnEmpty" | "selectable" | "labelInValue" | "treeCheckable" | "treeCheckStrictly" | "treeCheckedStrategy" | "disableFilter">;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        };
        $slots: Readonly<{
            [name: string]: import("vue").Slot | undefined;
        }>;
        $root: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $parent: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $emit: ((event: "update:popupVisible", visible: boolean) => void) & ((event: "clear") => void) & ((event: "change", value: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined) => void) & ((event: "update:inputValue", inputValue: string) => void) & ((event: "inputValueChange", inputValue: string) => void) & ((event: "update:modelValue", value: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined) => void) & ((event: "search", searchKey: string) => void) & ((event: "popup-visible-change", visible: boolean) => void);
        $el: any;
        $options: import("vue").ComponentOptionsBase<Readonly<{
            disabled?: unknown;
            loading?: unknown;
            error?: unknown;
            size?: unknown;
            border?: unknown;
            allowSearch?: unknown;
            allowClear?: unknown;
            placeholder?: unknown;
            maxTagCount?: unknown;
            multiple?: unknown;
            defaultValue?: unknown;
            modelValue?: unknown;
            fieldNames?: unknown;
            data?: unknown;
            labelInValue?: unknown;
            treeCheckable?: unknown;
            treeCheckStrictly?: unknown;
            treeCheckedStrategy?: unknown;
            treeProps?: unknown;
            triggerProps?: unknown;
            popupVisible?: unknown;
            defaultPopupVisible?: unknown;
            dropdownStyle?: unknown;
            dropdownClassName?: unknown;
            filterTreeNode?: unknown;
            loadMore?: unknown;
            disableFilter?: unknown;
            popupContainer?: unknown;
            fallbackOption?: unknown;
            selectable?: unknown;
            scrollbar?: unknown;
            showHeaderOnEmpty?: unknown;
            showFooterOnEmpty?: unknown;
            inputValue?: unknown;
            defaultInputValue?: unknown;
        } & {
            defaultPopupVisible: boolean;
            disabled: boolean;
            scrollbar: boolean | import("..").ScrollbarProps;
            border: boolean;
            multiple: boolean;
            data: import("..").TreeNodeData[];
            error: boolean;
            allowClear: boolean;
            loading: boolean;
            allowSearch: boolean | {
                retainInputValue?: boolean | undefined;
            };
            defaultInputValue: string;
            fallbackOption: boolean | ((key: string | number) => boolean | import("..").TreeNodeData);
            showHeaderOnEmpty: boolean;
            showFooterOnEmpty: boolean;
            selectable: boolean | "leaf" | ((node: import("..").TreeNodeData, info: {
                isLeaf: boolean;
                level: number;
            }) => boolean);
            labelInValue: boolean;
            treeCheckable: boolean;
            treeCheckStrictly: boolean;
            treeCheckedStrategy: "all" | "child" | "parent";
            disableFilter: boolean;
        } & {
            popupVisible?: boolean | undefined;
            popupContainer?: string | HTMLElement | undefined;
            size?: "mini" | "medium" | "large" | "small" | undefined;
            placeholder?: string | undefined;
            inputValue?: string | undefined;
            defaultValue?: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined;
            modelValue?: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined;
            maxTagCount?: number | undefined;
            fieldNames?: import("..").TreeFieldNames | undefined;
            triggerProps?: Partial<import("..").TriggerProps> | undefined;
            loadMore?: ((nodeData: import("..").TreeNodeData) => Promise<void>) | undefined;
            treeProps?: Partial<import("../tree/interface").TreeProps> | undefined;
            filterTreeNode?: ((searchKey: string, nodeData: import("..").TreeNodeData) => boolean) | undefined;
            dropdownStyle?: import("vue").CSSProperties | undefined;
            dropdownClassName?: string | string[] | undefined;
        }> & {
            "onUpdate:popupVisible"?: ((visible: boolean) => any) | undefined;
            onChange?: ((value: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined) => any) | undefined;
            onClear?: (() => any) | undefined;
            "onUpdate:inputValue"?: ((inputValue: string) => any) | undefined;
            onInputValueChange?: ((inputValue: string) => any) | undefined;
            "onUpdate:modelValue"?: ((value: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined) => any) | undefined;
            "onPopup-visible-change"?: ((visible: boolean) => any) | undefined;
            onSearch?: ((searchKey: string) => any) | undefined;
        }, {
            refSelectView: import("vue").Ref<any>;
            prefixCls: string;
            TreeSelectEmpty: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
                [key: string]: any;
            }> | undefined;
            selectedValue: import("vue").ComputedRef<import("./interface").LabelValue[]>;
            selectedKeys: import("vue").ComputedRef<import("../tree/interface").TreeNodeKey[]>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            searchValue: import("vue").ComputedRef<string>;
            panelVisible: import("vue").ComputedRef<boolean>;
            isEmpty: import("vue").ComputedRef<boolean | undefined>;
            computedFilterTreeNode: import("vue").ComputedRef<((node: import("..").TreeNodeData) => boolean) | undefined>;
            isMultiple: import("vue").ComputedRef<boolean>;
            selectViewValue: import("vue").ComputedRef<import("../_components/select-view/interface").SelectViewValue[]>;
            computedDropdownStyle: import("vue").ComputedRef<import("vue").StyleValue[]>;
            onSearchValueChange: (inputValue: string) => void;
            onSelectChange(newVal: string[]): void;
            onVisibleChange: (visible: boolean) => void;
            onInnerClear(): void;
            pickSubCompSlots: typeof import("../_utils/pick-sub-comp-slots").default;
            isSelectable: (node: import("..").TreeNodeData, info: {
                level: number;
                isLeaf: boolean;
            }) => boolean;
            isCheckable: import("vue").ComputedRef<false | ((node: import("..").TreeNodeData, info: {
                level: number;
                isLeaf: boolean;
            }) => boolean)>;
            onBlur: () => void;
            onItemRemove(id: string): void;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            change: (value: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined) => true;
            'update:modelValue': (value: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined) => true;
            'update:inputValue': (inputValue: string) => true;
            'popup-visible-change': (visible: boolean) => true;
            'update:popupVisible': (visible: boolean) => true;
            search: (searchKey: string) => true;
            clear: () => true;
            inputValueChange: (inputValue: string) => true;
        }, string, {
            popupVisible: boolean;
            defaultPopupVisible: boolean;
            disabled: boolean;
            scrollbar: boolean | import("..").ScrollbarProps;
            border: boolean;
            multiple: boolean;
            data: import("..").TreeNodeData[];
            error: boolean;
            allowClear: boolean;
            loading: boolean;
            allowSearch: boolean | {
                retainInputValue?: boolean | undefined;
            };
            defaultInputValue: string;
            fallbackOption: boolean | ((key: string | number) => boolean | import("..").TreeNodeData);
            showHeaderOnEmpty: boolean;
            showFooterOnEmpty: boolean;
            selectable: boolean | "leaf" | ((node: import("..").TreeNodeData, info: {
                isLeaf: boolean;
                level: number;
            }) => boolean);
            labelInValue: boolean;
            treeCheckable: boolean;
            treeCheckStrictly: boolean;
            treeCheckedStrategy: "all" | "child" | "parent";
            disableFilter: boolean;
        }> & {
            beforeCreate?: ((() => void) | (() => void)[]) | undefined;
            created?: ((() => void) | (() => void)[]) | undefined;
            beforeMount?: ((() => void) | (() => void)[]) | undefined;
            mounted?: ((() => void) | (() => void)[]) | undefined;
            beforeUpdate?: ((() => void) | (() => void)[]) | undefined;
            updated?: ((() => void) | (() => void)[]) | undefined;
            activated?: ((() => void) | (() => void)[]) | undefined;
            deactivated?: ((() => void) | (() => void)[]) | undefined;
            beforeDestroy?: ((() => void) | (() => void)[]) | undefined;
            beforeUnmount?: ((() => void) | (() => void)[]) | undefined;
            destroyed?: ((() => void) | (() => void)[]) | undefined;
            unmounted?: ((() => void) | (() => void)[]) | undefined;
            renderTracked?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            renderTriggered?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            errorCaptured?: (((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void) | ((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void)[]) | undefined;
        };
        $forceUpdate: () => void;
        $nextTick: typeof import("vue").nextTick;
        $watch(source: string | Function, cb: Function, options?: import("vue").WatchOptions<boolean> | undefined): import("vue").WatchStopHandle;
    } & Readonly<{
        disabled?: unknown;
        loading?: unknown;
        error?: unknown;
        size?: unknown;
        border?: unknown;
        allowSearch?: unknown;
        allowClear?: unknown;
        placeholder?: unknown;
        maxTagCount?: unknown;
        multiple?: unknown;
        defaultValue?: unknown;
        modelValue?: unknown;
        fieldNames?: unknown;
        data?: unknown;
        labelInValue?: unknown;
        treeCheckable?: unknown;
        treeCheckStrictly?: unknown;
        treeCheckedStrategy?: unknown;
        treeProps?: unknown;
        triggerProps?: unknown;
        popupVisible?: unknown;
        defaultPopupVisible?: unknown;
        dropdownStyle?: unknown;
        dropdownClassName?: unknown;
        filterTreeNode?: unknown;
        loadMore?: unknown;
        disableFilter?: unknown;
        popupContainer?: unknown;
        fallbackOption?: unknown;
        selectable?: unknown;
        scrollbar?: unknown;
        showHeaderOnEmpty?: unknown;
        showFooterOnEmpty?: unknown;
        inputValue?: unknown;
        defaultInputValue?: unknown;
    } & {
        defaultPopupVisible: boolean;
        disabled: boolean;
        scrollbar: boolean | import("..").ScrollbarProps;
        border: boolean;
        multiple: boolean;
        data: import("..").TreeNodeData[];
        error: boolean;
        allowClear: boolean;
        loading: boolean;
        allowSearch: boolean | {
            retainInputValue?: boolean | undefined;
        };
        defaultInputValue: string;
        fallbackOption: boolean | ((key: string | number) => boolean | import("..").TreeNodeData);
        showHeaderOnEmpty: boolean;
        showFooterOnEmpty: boolean;
        selectable: boolean | "leaf" | ((node: import("..").TreeNodeData, info: {
            isLeaf: boolean;
            level: number;
        }) => boolean);
        labelInValue: boolean;
        treeCheckable: boolean;
        treeCheckStrictly: boolean;
        treeCheckedStrategy: "all" | "child" | "parent";
        disableFilter: boolean;
    } & {
        popupVisible?: boolean | undefined;
        popupContainer?: string | HTMLElement | undefined;
        size?: "mini" | "medium" | "large" | "small" | undefined;
        placeholder?: string | undefined;
        inputValue?: string | undefined;
        defaultValue?: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined;
        modelValue?: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined;
        maxTagCount?: number | undefined;
        fieldNames?: import("..").TreeFieldNames | undefined;
        triggerProps?: Partial<import("..").TriggerProps> | undefined;
        loadMore?: ((nodeData: import("..").TreeNodeData) => Promise<void>) | undefined;
        treeProps?: Partial<import("../tree/interface").TreeProps> | undefined;
        filterTreeNode?: ((searchKey: string, nodeData: import("..").TreeNodeData) => boolean) | undefined;
        dropdownStyle?: import("vue").CSSProperties | undefined;
        dropdownClassName?: string | string[] | undefined;
    }> & {
        "onUpdate:popupVisible"?: ((visible: boolean) => any) | undefined;
        onChange?: ((value: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined) => any) | undefined;
        onClear?: (() => any) | undefined;
        "onUpdate:inputValue"?: ((inputValue: string) => any) | undefined;
        onInputValueChange?: ((inputValue: string) => any) | undefined;
        "onUpdate:modelValue"?: ((value: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined) => any) | undefined;
        "onPopup-visible-change"?: ((visible: boolean) => any) | undefined;
        onSearch?: ((searchKey: string) => any) | undefined;
    } & import("vue").ShallowUnwrapRef<{
        refSelectView: import("vue").Ref<any>;
        prefixCls: string;
        TreeSelectEmpty: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
            [key: string]: any;
        }> | undefined;
        selectedValue: import("vue").ComputedRef<import("./interface").LabelValue[]>;
        selectedKeys: import("vue").ComputedRef<import("../tree/interface").TreeNodeKey[]>;
        mergedDisabled: import("vue").ComputedRef<boolean>;
        searchValue: import("vue").ComputedRef<string>;
        panelVisible: import("vue").ComputedRef<boolean>;
        isEmpty: import("vue").ComputedRef<boolean | undefined>;
        computedFilterTreeNode: import("vue").ComputedRef<((node: import("..").TreeNodeData) => boolean) | undefined>;
        isMultiple: import("vue").ComputedRef<boolean>;
        selectViewValue: import("vue").ComputedRef<import("../_components/select-view/interface").SelectViewValue[]>;
        computedDropdownStyle: import("vue").ComputedRef<import("vue").StyleValue[]>;
        onSearchValueChange: (inputValue: string) => void;
        onSelectChange(newVal: string[]): void;
        onVisibleChange: (visible: boolean) => void;
        onInnerClear(): void;
        pickSubCompSlots: typeof import("../_utils/pick-sub-comp-slots").default;
        isSelectable: (node: import("..").TreeNodeData, info: {
            level: number;
            isLeaf: boolean;
        }) => boolean;
        isCheckable: import("vue").ComputedRef<false | ((node: import("..").TreeNodeData, info: {
            level: number;
            isLeaf: boolean;
        }) => boolean)>;
        onBlur: () => void;
        onItemRemove(id: string): void;
    }> & {} & {} & import("vue").ComponentCustomProperties;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<{
    disabled?: unknown;
    loading?: unknown;
    error?: unknown;
    size?: unknown;
    border?: unknown;
    allowSearch?: unknown;
    allowClear?: unknown;
    placeholder?: unknown;
    maxTagCount?: unknown;
    multiple?: unknown;
    defaultValue?: unknown;
    modelValue?: unknown;
    fieldNames?: unknown;
    data?: unknown;
    labelInValue?: unknown;
    treeCheckable?: unknown;
    treeCheckStrictly?: unknown;
    treeCheckedStrategy?: unknown;
    treeProps?: unknown;
    triggerProps?: unknown;
    popupVisible?: unknown;
    defaultPopupVisible?: unknown;
    dropdownStyle?: unknown;
    dropdownClassName?: unknown;
    filterTreeNode?: unknown;
    loadMore?: unknown;
    disableFilter?: unknown;
    popupContainer?: unknown;
    fallbackOption?: unknown;
    selectable?: unknown;
    scrollbar?: unknown;
    showHeaderOnEmpty?: unknown;
    showFooterOnEmpty?: unknown;
    inputValue?: unknown;
    defaultInputValue?: unknown;
} & {
    defaultPopupVisible: boolean;
    disabled: boolean;
    scrollbar: boolean | import("..").ScrollbarProps;
    border: boolean;
    multiple: boolean;
    data: import("..").TreeNodeData[];
    error: boolean;
    allowClear: boolean;
    loading: boolean;
    allowSearch: boolean | {
        retainInputValue?: boolean | undefined;
    };
    defaultInputValue: string;
    fallbackOption: boolean | ((key: string | number) => boolean | import("..").TreeNodeData);
    showHeaderOnEmpty: boolean;
    showFooterOnEmpty: boolean;
    selectable: boolean | "leaf" | ((node: import("..").TreeNodeData, info: {
        isLeaf: boolean;
        level: number;
    }) => boolean);
    labelInValue: boolean;
    treeCheckable: boolean;
    treeCheckStrictly: boolean;
    treeCheckedStrategy: "all" | "child" | "parent";
    disableFilter: boolean;
} & {
    popupVisible?: boolean | undefined;
    popupContainer?: string | HTMLElement | undefined;
    size?: "mini" | "medium" | "large" | "small" | undefined;
    placeholder?: string | undefined;
    inputValue?: string | undefined;
    defaultValue?: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined;
    modelValue?: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined;
    maxTagCount?: number | undefined;
    fieldNames?: import("..").TreeFieldNames | undefined;
    triggerProps?: Partial<import("..").TriggerProps> | undefined;
    loadMore?: ((nodeData: import("..").TreeNodeData) => Promise<void>) | undefined;
    treeProps?: Partial<import("../tree/interface").TreeProps> | undefined;
    filterTreeNode?: ((searchKey: string, nodeData: import("..").TreeNodeData) => boolean) | undefined;
    dropdownStyle?: import("vue").CSSProperties | undefined;
    dropdownClassName?: string | string[] | undefined;
}> & {
    "onUpdate:popupVisible"?: ((visible: boolean) => any) | undefined;
    onChange?: ((value: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined) => any) | undefined;
    onClear?: (() => any) | undefined;
    "onUpdate:inputValue"?: ((inputValue: string) => any) | undefined;
    onInputValueChange?: ((inputValue: string) => any) | undefined;
    "onUpdate:modelValue"?: ((value: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined) => any) | undefined;
    "onPopup-visible-change"?: ((visible: boolean) => any) | undefined;
    onSearch?: ((searchKey: string) => any) | undefined;
}, {
    refSelectView: import("vue").Ref<any>;
    prefixCls: string;
    TreeSelectEmpty: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }> | undefined;
    selectedValue: import("vue").ComputedRef<import("./interface").LabelValue[]>;
    selectedKeys: import("vue").ComputedRef<import("../tree/interface").TreeNodeKey[]>;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    searchValue: import("vue").ComputedRef<string>;
    panelVisible: import("vue").ComputedRef<boolean>;
    isEmpty: import("vue").ComputedRef<boolean | undefined>;
    computedFilterTreeNode: import("vue").ComputedRef<((node: import("..").TreeNodeData) => boolean) | undefined>;
    isMultiple: import("vue").ComputedRef<boolean>;
    selectViewValue: import("vue").ComputedRef<import("../_components/select-view/interface").SelectViewValue[]>;
    computedDropdownStyle: import("vue").ComputedRef<import("vue").StyleValue[]>;
    onSearchValueChange: (inputValue: string) => void;
    onSelectChange(newVal: string[]): void;
    onVisibleChange: (visible: boolean) => void;
    onInnerClear(): void;
    pickSubCompSlots: typeof import("../_utils/pick-sub-comp-slots").default;
    isSelectable: (node: import("..").TreeNodeData, info: {
        level: number;
        isLeaf: boolean;
    }) => boolean;
    isCheckable: import("vue").ComputedRef<false | ((node: import("..").TreeNodeData, info: {
        level: number;
        isLeaf: boolean;
    }) => boolean)>;
    onBlur: () => void;
    onItemRemove(id: string): void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    change: (value: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined) => true;
    'update:modelValue': (value: string | number | (string | number)[] | import("./interface").LabelValue | import("./interface").LabelValue[] | undefined) => true;
    'update:inputValue': (inputValue: string) => true;
    'popup-visible-change': (visible: boolean) => true;
    'update:popupVisible': (visible: boolean) => true;
    search: (searchKey: string) => true;
    clear: () => true;
    inputValueChange: (inputValue: string) => true;
}, string, {
    popupVisible: boolean;
    defaultPopupVisible: boolean;
    disabled: boolean;
    scrollbar: boolean | import("..").ScrollbarProps;
    border: boolean;
    multiple: boolean;
    data: import("..").TreeNodeData[];
    error: boolean;
    allowClear: boolean;
    loading: boolean;
    allowSearch: boolean | {
        retainInputValue?: boolean | undefined;
    };
    defaultInputValue: string;
    fallbackOption: boolean | ((key: string | number) => boolean | import("..").TreeNodeData);
    showHeaderOnEmpty: boolean;
    showFooterOnEmpty: boolean;
    selectable: boolean | "leaf" | ((node: import("..").TreeNodeData, info: {
        isLeaf: boolean;
        level: number;
    }) => boolean);
    labelInValue: boolean;
    treeCheckable: boolean;
    treeCheckStrictly: boolean;
    treeCheckedStrategy: "all" | "child" | "parent";
    disableFilter: boolean;
}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type TreeSelectInstance = InstanceType<typeof _TreeSelect>;
export default TreeSelect;
