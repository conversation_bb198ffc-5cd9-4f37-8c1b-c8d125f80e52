import { CSSProperties, PropType, StyleValue } from 'vue';
import { LabelValue } from './interface';
import { TriggerProps } from '../trigger';
import { TreeFieldNames, TreeNodeData, TreeProps, TreeNodeKey } from '../tree/interface';
import pickSubCompSlots from '../_utils/pick-sub-comp-slots';
import { Data } from '../_utils/types';
import { ScrollbarProps } from '../scrollbar';
import { SelectViewValue } from '../_components/select-view/interface';
declare const _default: import("vue").DefineComponent<{
    disabled: {
        type: BooleanConstructor;
    };
    loading: {
        type: BooleanConstructor;
    };
    error: {
        type: BooleanConstructor;
    };
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    border: {
        type: BooleanConstructor;
        default: boolean;
    };
    allowSearch: {
        type: PropType<boolean | {
            retainInputValue?: boolean | undefined;
        }>;
        default: (props: Data) => boolean;
    };
    allowClear: {
        type: BooleanConstructor;
    };
    placeholder: {
        type: StringConstructor;
    };
    maxTagCount: {
        type: NumberConstructor;
    };
    multiple: {
        type: BooleanConstructor;
    };
    defaultValue: {
        type: PropType<string | number | (string | number)[] | LabelValue | LabelValue[]>;
    };
    modelValue: {
        type: PropType<string | number | (string | number)[] | LabelValue | LabelValue[]>;
    };
    fieldNames: {
        type: PropType<TreeFieldNames>;
    };
    data: {
        type: PropType<TreeNodeData[]>;
        default: () => never[];
    };
    labelInValue: {
        type: BooleanConstructor;
    };
    treeCheckable: {
        type: BooleanConstructor;
    };
    treeCheckStrictly: {
        type: BooleanConstructor;
    };
    treeCheckedStrategy: {
        type: PropType<"all" | "child" | "parent">;
        default: string;
    };
    treeProps: {
        type: PropType<Partial<TreeProps>>;
    };
    triggerProps: {
        type: PropType<Partial<TriggerProps>>;
    };
    popupVisible: {
        type: BooleanConstructor;
        default: undefined;
    };
    defaultPopupVisible: {
        type: BooleanConstructor;
    };
    dropdownStyle: {
        type: PropType<CSSProperties>;
    };
    dropdownClassName: {
        type: PropType<string | string[]>;
    };
    filterTreeNode: {
        type: PropType<(searchKey: string, nodeData: TreeNodeData) => boolean>;
    };
    loadMore: {
        type: PropType<(nodeData: TreeNodeData) => Promise<void>>;
    };
    disableFilter: {
        type: BooleanConstructor;
    };
    popupContainer: {
        type: PropType<string | HTMLElement>;
    };
    fallbackOption: {
        type: PropType<boolean | ((key: number | string) => TreeNodeData | boolean)>;
        default: boolean;
    };
    selectable: {
        type: PropType<boolean | "leaf" | ((node: TreeNodeData, info: {
            isLeaf: boolean;
            level: number;
        }) => boolean)>;
        default: boolean;
    };
    scrollbar: {
        type: PropType<boolean | ScrollbarProps>;
        default: boolean;
    };
    showHeaderOnEmpty: {
        type: PropType<boolean>;
        default: boolean;
    };
    showFooterOnEmpty: {
        type: PropType<boolean>;
        default: boolean;
    };
    inputValue: {
        type: StringConstructor;
    };
    defaultInputValue: {
        type: StringConstructor;
        default: string;
    };
}, {
    refSelectView: import("vue").Ref<any>;
    prefixCls: string;
    TreeSelectEmpty: import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }> | undefined;
    selectedValue: import("vue").ComputedRef<LabelValue[]>;
    selectedKeys: import("vue").ComputedRef<TreeNodeKey[]>;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    searchValue: import("vue").ComputedRef<string>;
    panelVisible: import("vue").ComputedRef<boolean>;
    isEmpty: import("vue").ComputedRef<boolean | undefined>;
    computedFilterTreeNode: import("vue").ComputedRef<((node: TreeNodeData) => boolean) | undefined>;
    isMultiple: import("vue").ComputedRef<boolean>;
    selectViewValue: import("vue").ComputedRef<SelectViewValue[]>;
    computedDropdownStyle: import("vue").ComputedRef<StyleValue[]>;
    onSearchValueChange: (inputValue: string) => void;
    onSelectChange(newVal: string[]): void;
    onVisibleChange: (visible: boolean) => void;
    onInnerClear(): void;
    pickSubCompSlots: typeof pickSubCompSlots;
    isSelectable: (node: TreeNodeData, info: {
        level: number;
        isLeaf: boolean;
    }) => boolean;
    isCheckable: import("vue").ComputedRef<false | ((node: TreeNodeData, info: {
        level: number;
        isLeaf: boolean;
    }) => boolean)>;
    onBlur: () => void;
    onItemRemove(id: string): void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    change: (value: string | number | LabelValue | Array<string | number> | LabelValue[] | undefined) => true;
    'update:modelValue': (value: string | number | LabelValue | Array<string | number> | LabelValue[] | undefined) => true;
    'update:inputValue': (inputValue: string) => true;
    'popup-visible-change': (visible: boolean) => true;
    'update:popupVisible': (visible: boolean) => true;
    search: (searchKey: string) => true;
    clear: () => true;
    inputValueChange: (inputValue: string) => true;
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    disabled?: unknown;
    loading?: unknown;
    error?: unknown;
    size?: unknown;
    border?: unknown;
    allowSearch?: unknown;
    allowClear?: unknown;
    placeholder?: unknown;
    maxTagCount?: unknown;
    multiple?: unknown;
    defaultValue?: unknown;
    modelValue?: unknown;
    fieldNames?: unknown;
    data?: unknown;
    labelInValue?: unknown;
    treeCheckable?: unknown;
    treeCheckStrictly?: unknown;
    treeCheckedStrategy?: unknown;
    treeProps?: unknown;
    triggerProps?: unknown;
    popupVisible?: unknown;
    defaultPopupVisible?: unknown;
    dropdownStyle?: unknown;
    dropdownClassName?: unknown;
    filterTreeNode?: unknown;
    loadMore?: unknown;
    disableFilter?: unknown;
    popupContainer?: unknown;
    fallbackOption?: unknown;
    selectable?: unknown;
    scrollbar?: unknown;
    showHeaderOnEmpty?: unknown;
    showFooterOnEmpty?: unknown;
    inputValue?: unknown;
    defaultInputValue?: unknown;
} & {
    defaultPopupVisible: boolean;
    disabled: boolean;
    scrollbar: boolean | ScrollbarProps;
    border: boolean;
    multiple: boolean;
    data: TreeNodeData[];
    error: boolean;
    allowClear: boolean;
    loading: boolean;
    allowSearch: boolean | {
        retainInputValue?: boolean | undefined;
    };
    defaultInputValue: string;
    fallbackOption: boolean | ((key: number | string) => TreeNodeData | boolean);
    showHeaderOnEmpty: boolean;
    showFooterOnEmpty: boolean;
    selectable: boolean | "leaf" | ((node: TreeNodeData, info: {
        isLeaf: boolean;
        level: number;
    }) => boolean);
    labelInValue: boolean;
    treeCheckable: boolean;
    treeCheckStrictly: boolean;
    treeCheckedStrategy: "all" | "child" | "parent";
    disableFilter: boolean;
} & {
    popupVisible?: boolean | undefined;
    popupContainer?: string | HTMLElement | undefined;
    size?: "mini" | "medium" | "large" | "small" | undefined;
    placeholder?: string | undefined;
    inputValue?: string | undefined;
    defaultValue?: string | number | (string | number)[] | LabelValue | LabelValue[] | undefined;
    modelValue?: string | number | (string | number)[] | LabelValue | LabelValue[] | undefined;
    maxTagCount?: number | undefined;
    fieldNames?: TreeFieldNames | undefined;
    triggerProps?: Partial<TriggerProps> | undefined;
    loadMore?: ((nodeData: TreeNodeData) => Promise<void>) | undefined;
    treeProps?: Partial<TreeProps> | undefined;
    filterTreeNode?: ((searchKey: string, nodeData: TreeNodeData) => boolean) | undefined;
    dropdownStyle?: CSSProperties | undefined;
    dropdownClassName?: string | string[] | undefined;
}> & {
    "onUpdate:popupVisible"?: ((visible: boolean) => any) | undefined;
    onChange?: ((value: string | number | (string | number)[] | LabelValue | LabelValue[] | undefined) => any) | undefined;
    onClear?: (() => any) | undefined;
    "onUpdate:inputValue"?: ((inputValue: string) => any) | undefined;
    onInputValueChange?: ((inputValue: string) => any) | undefined;
    "onUpdate:modelValue"?: ((value: string | number | (string | number)[] | LabelValue | LabelValue[] | undefined) => any) | undefined;
    "onPopup-visible-change"?: ((visible: boolean) => any) | undefined;
    onSearch?: ((searchKey: string) => any) | undefined;
}, {
    popupVisible: boolean;
    defaultPopupVisible: boolean;
    disabled: boolean;
    scrollbar: boolean | ScrollbarProps;
    border: boolean;
    multiple: boolean;
    data: TreeNodeData[];
    error: boolean;
    allowClear: boolean;
    loading: boolean;
    allowSearch: boolean | {
        retainInputValue?: boolean | undefined;
    };
    defaultInputValue: string;
    fallbackOption: boolean | ((key: number | string) => TreeNodeData | boolean);
    showHeaderOnEmpty: boolean;
    showFooterOnEmpty: boolean;
    selectable: boolean | "leaf" | ((node: TreeNodeData, info: {
        isLeaf: boolean;
        level: number;
    }) => boolean);
    labelInValue: boolean;
    treeCheckable: boolean;
    treeCheckStrictly: boolean;
    treeCheckedStrategy: "all" | "child" | "parent";
    disableFilter: boolean;
}>;
export default _default;
