import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _Typography from './typography';
import _TypographyParagraph from './paragraph';
import _TypographyTitle from './title';
import _TypographyText from './text';
export type { EllipsisConfig } from './interface';
declare const Typography: {
    new (...args: any[]): {
        $: import("vue").ComponentInternalInstance;
        $data: {};
        $props: Partial<{}> & Omit<Readonly<{} & {} & {}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, never>;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        };
        $slots: Readonly<{
            [name: string]: import("vue").Slot | undefined;
        }>;
        $root: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $parent: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $emit: ((event: string, ...args: any[]) => void) | ((event: string, ...args: any[]) => void);
        $el: any;
        $options: import("vue").ComponentOptionsBase<Readonly<{} & {} & {}>, {
            classNames: string[];
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, import("vue").EmitsOptions, string, {}> & {
            beforeCreate?: ((() => void) | (() => void)[]) | undefined;
            created?: ((() => void) | (() => void)[]) | undefined;
            beforeMount?: ((() => void) | (() => void)[]) | undefined;
            mounted?: ((() => void) | (() => void)[]) | undefined;
            beforeUpdate?: ((() => void) | (() => void)[]) | undefined;
            updated?: ((() => void) | (() => void)[]) | undefined;
            activated?: ((() => void) | (() => void)[]) | undefined;
            deactivated?: ((() => void) | (() => void)[]) | undefined;
            beforeDestroy?: ((() => void) | (() => void)[]) | undefined;
            beforeUnmount?: ((() => void) | (() => void)[]) | undefined;
            destroyed?: ((() => void) | (() => void)[]) | undefined;
            unmounted?: ((() => void) | (() => void)[]) | undefined;
            renderTracked?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            renderTriggered?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            errorCaptured?: (((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void) | ((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void)[]) | undefined;
        };
        $forceUpdate: () => void;
        $nextTick: typeof import("vue").nextTick;
        $watch(source: string | Function, cb: Function, options?: import("vue").WatchOptions<boolean> | undefined): import("vue").WatchStopHandle;
    } & Readonly<{} & {} & {}> & import("vue").ShallowUnwrapRef<{
        classNames: string[];
    }> & {} & {} & import("vue").ComponentCustomProperties;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<{} & {} & {}>, {
    classNames: string[];
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, import("vue").EmitsOptions, string, {}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    Paragraph: import("vue").DefineComponent<{
        blockquote: {
            type: BooleanConstructor;
        };
        spacing: {
            type: import("vue").PropType<"default" | "close">;
            default: string;
        };
    }, {
        component: import("vue").ComputedRef<"div" | "blockquote">;
        classNames: import("vue").ComputedRef<{
            [x: string]: boolean;
        }[]>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
        blockquote?: unknown;
        spacing?: unknown;
    } & {
        blockquote: boolean;
        spacing: "default" | "close";
    } & {}>, {
        blockquote: boolean;
        spacing: "default" | "close";
    }>;
    Title: import("vue").DefineComponent<{
        heading: {
            type: import("vue").PropType<2 | 1 | 4 | 3 | 6 | 5>;
            default: number;
        };
    }, {
        component: import("vue").ComputedRef<keyof HTMLElementTagNameMap>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
        heading?: unknown;
    } & {
        heading: 2 | 1 | 4 | 3 | 6 | 5;
    } & {}>, {
        heading: 2 | 1 | 4 | 3 | 6 | 5;
    }>;
    Text: import("vue").DefineComponent<{
        ellipsis: {
            type: import("vue").PropType<boolean | import("./interface").EllipsisConfig>;
            default: boolean;
        };
    }, {
        component: import("vue").ComputedRef<"div" | "span">;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
        ellipsis?: unknown;
    } & {
        ellipsis: boolean | import("./interface").EllipsisConfig;
    } & {}>, {
        ellipsis: boolean | import("./interface").EllipsisConfig;
    }>;
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type TypographyInstance = InstanceType<typeof _Typography>;
export declare type TypographyParagraphInstance = InstanceType<typeof _TypographyParagraph>;
export declare type TypographyTitleInstance = InstanceType<typeof _TypographyTitle>;
export declare type TypographyTextInstance = InstanceType<typeof _TypographyText>;
export { _TypographyParagraph as TypographyParagraph, _TypographyTitle as TypographyTitle, _TypographyText as TypographyText, };
export default Typography;
