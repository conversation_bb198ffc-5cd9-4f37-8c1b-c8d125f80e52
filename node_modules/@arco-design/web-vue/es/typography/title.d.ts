import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<{
    heading: {
        type: PropType<2 | 1 | 4 | 3 | 6 | 5>;
        default: number;
    };
}, {
    component: import("vue").ComputedRef<keyof HTMLElementTagNameMap>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    heading?: unknown;
} & {
    heading: 2 | 1 | 4 | 3 | 6 | 5;
} & {}>, {
    heading: 2 | 1 | 4 | 3 | 6 | 5;
}>;
export default _default;
