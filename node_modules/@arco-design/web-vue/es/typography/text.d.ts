import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<{
    ellipsis: {
        type: PropType<boolean | import("./interface").EllipsisConfig>;
        default: boolean;
    };
}, {
    component: import("vue").ComputedRef<"div" | "span">;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    ellipsis?: unknown;
} & {
    ellipsis: boolean | import("./interface").EllipsisConfig;
} & {}>, {
    ellipsis: boolean | import("./interface").EllipsisConfig;
}>;
export default _default;
