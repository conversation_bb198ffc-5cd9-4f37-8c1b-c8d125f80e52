/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
/******** 基础配置项 end *******/
/******** 基础配置项 end *******/
/******** 高级配置项 *******/
/******** 高级配置项 end *******/
.arco-typography {
  color: var(--color-text-1);
  line-height: 1.5715;
  white-space: normal;
  overflow-wrap: anywhere;
}
h1.arco-typography,
h2.arco-typography,
h3.arco-typography,
h4.arco-typography,
h5.arco-typography,
h6.arco-typography {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: 500;
}
h1.arco-typography {
  font-size: 36px;
  line-height: 1.23;
}
h2.arco-typography {
  font-size: 32px;
  line-height: 1.25;
}
h3.arco-typography {
  font-size: 28px;
  line-height: 1.29;
}
h4.arco-typography {
  font-size: 24px;
  line-height: 1.33;
}
h5.arco-typography {
  font-size: 20px;
  line-height: 1.4;
}
h6.arco-typography {
  font-size: 16px;
  line-height: 1.5;
}
div.arco-typography,
p.arco-typography {
  margin-top: 0;
  margin-bottom: 1em;
}
.arco-typography-primary {
  color: rgb(var(--primary-6));
}
.arco-typography-secondary {
  color: var(--color-text-2);
}
.arco-typography-success {
  color: rgb(var(--success-6));
}
.arco-typography-warning {
  color: rgb(var(--warning-6));
}
.arco-typography-danger {
  color: rgb(var(--danger-6));
}
.arco-typography-disabled {
  color: var(--color-text-4);
  cursor: not-allowed;
}
.arco-typography mark {
  background-color: rgb(var(--yellow-4));
}
.arco-typography u {
  text-decoration: underline;
}
.arco-typography del {
  text-decoration: line-through;
}
.arco-typography b {
  font-weight: 500;
}
.arco-typography code {
  margin: 0 2px;
  padding: 2px 8px;
  color: var(--color-text-2);
  font-size: 85%;
  background-color: var(--color-neutral-2);
  border: 1px solid var(--color-neutral-3);
  border-radius: 2px;
}
.arco-typography blockquote {
  margin: 0;
  margin-bottom: 1em;
  padding-left: 8px;
  background-color: var(--color-bg-2);
  border-left: 2px solid var(--color-neutral-6);
}
.arco-typography ol,
.arco-typography ul {
  margin: 0;
  padding: 0;
}
.arco-typography ul li,
.arco-typography ol li {
  margin-left: 20px;
}
.arco-typography ul {
  list-style: circle;
}
.arco-typography-spacing-close {
  line-height: 1.3;
}
.arco-typography-operation-copy,
.arco-typography-operation-copied {
  margin-left: 2px;
  padding: 2px;
}
.arco-typography-operation-copy {
  color: var(--color-text-2);
  background-color: transparent;
  border-radius: 2px;
  cursor: pointer;
  transition: background-color 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-typography-operation-copy:hover {
  color: var(--color-text-2);
  background-color: var(--color-fill-2);
}
.arco-typography-operation-copied {
  color: rgb(var(--success-6));
}
.arco-typography-operation-edit {
  margin-left: 2px;
  padding: 2px;
  color: var(--color-text-2);
  background-color: transparent;
  border-radius: 2px;
  cursor: pointer;
  transition: background-color 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-typography-operation-edit:hover {
  color: var(--color-text-2);
  background-color: var(--color-fill-2);
}
.arco-typography-operation-expand {
  margin: 0 4px;
  color: rgb(var(--primary-6));
  cursor: pointer;
}
.arco-typography-operation-expand:hover {
  color: rgb(var(--primary-5));
}
.arco-typography-edit-content {
  position: relative;
  left: -13px;
  margin-top: -5px;
  margin-right: -13px;
  margin-bottom: calc(1em - 4px - 1px);
}
.arco-typography-css-operation {
  margin-top: -1em;
  margin-bottom: 1em;
  text-align: right;
}
