import { defineComponent, toRefs, reactive, computed, resolveComponent, openBlock, createElementBlock, normalizeClass, Fragment, renderList, createCommentVNode, createElementVNode, toDisplayString, createBlock } from "vue";
import { isFunction } from "../../_utils/is.js";
import useCellClassName from "../hooks/use-cell-class-name.js";
import RenderFunction from "../../_components/render-function.js";
import { getDateValue } from "../../_utils/date.js";
import _export_sfc from "../../_virtual/plugin-vue_export-helper.js";
const _sfc_main = defineComponent({
  name: "PanelBody",
  components: {
    RenderFunction
  },
  props: {
    prefixCls: {
      type: String,
      required: true
    },
    rows: {
      type: Array,
      default: () => []
    },
    value: {
      type: Object
    },
    disabledDate: {
      type: Function
    },
    isSameTime: {
      type: Function,
      required: true
    },
    mode: {
      type: String
    },
    rangeValues: {
      type: Array
    },
    dateRender: {
      type: Function
    }
  },
  emits: ["cell-click", "cell-mouse-enter"],
  setup(props, { emit }) {
    const { prefixCls, value, disabledDate, isSameTime, mode, rangeValues } = toRefs(props);
    const { getCellClassName } = useCellClassName(reactive({
      prefixCls,
      value,
      isSameTime,
      mode,
      rangeValues
    }));
    const isCellDisabled = (cellData) => !!(isFunction(disabledDate == null ? void 0 : disabledDate.value) && (disabledDate == null ? void 0 : disabledDate.value(getDateValue(cellData.value))));
    return {
      isWeek: computed(() => (mode == null ? void 0 : mode.value) === "week"),
      getCellClassName: (cellData) => {
        const disabled = isCellDisabled(cellData);
        return getCellClassName(cellData, disabled);
      },
      onCellClick: (cellData) => {
        const disabled = isCellDisabled(cellData);
        if (disabled)
          return;
        emit("cell-click", cellData);
      },
      onCellMouseEnter: (cellData) => {
        const disabled = isCellDisabled(cellData);
        if (disabled)
          return;
        emit("cell-mouse-enter", cellData);
      },
      onCellMouseLeave: (cellData) => {
        const disabled = isCellDisabled(cellData);
        if (disabled)
          return;
        emit("cell-mouse-enter", cellData);
      },
      getDateValue
    };
  }
});
const _hoisted_1 = ["onMouseenter", "onMouseleave", "onClick"];
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  const _component_RenderFunction = resolveComponent("RenderFunction");
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(`${_ctx.prefixCls}-body`)
  }, [
    (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.rows, (row, rowIndex) => {
      return openBlock(), createElementBlock("div", {
        key: rowIndex,
        class: normalizeClass([
          `${_ctx.prefixCls}-row`,
          {
            [`${_ctx.prefixCls}-row-week`]: _ctx.isWeek
          }
        ])
      }, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(row, (cell, colIndex) => {
          return openBlock(), createElementBlock(Fragment, null, [
            createCommentVNode(" \u4E00\u5E74\u4E2D\u7684\u7B2C\u51E0\u5468\uFF0C\u53EA\u5728 week \u6A21\u5F0F\u4E0B\u663E\u793A "),
            _ctx.isWeek && colIndex === 0 ? (openBlock(), createElementBlock("div", {
              key: colIndex,
              class: normalizeClass([`${_ctx.prefixCls}-cell`, `${_ctx.prefixCls}-cell-week`])
            }, [
              createElementVNode("div", {
                class: normalizeClass(`${_ctx.prefixCls}-date`)
              }, [
                createElementVNode("div", {
                  class: normalizeClass(`${_ctx.prefixCls}-date-value`)
                }, toDisplayString(cell.label), 3)
              ], 2)
            ], 2)) : (openBlock(), createElementBlock("div", {
              key: colIndex,
              class: normalizeClass(_ctx.getCellClassName(cell)),
              onMouseenter: () => {
                _ctx.onCellMouseEnter(cell);
              },
              onMouseleave: () => {
                _ctx.onCellMouseLeave(cell);
              },
              onClick: () => {
                _ctx.onCellClick(cell);
              }
            }, [
              _ctx.dateRender ? (openBlock(), createBlock(_component_RenderFunction, {
                key: 0,
                "render-func": _ctx.dateRender,
                date: _ctx.getDateValue(cell.value)
              }, null, 8, ["render-func", "date"])) : (openBlock(), createElementBlock("div", {
                key: 1,
                class: normalizeClass(`${_ctx.prefixCls}-date`)
              }, [
                createElementVNode("div", {
                  class: normalizeClass(`${_ctx.prefixCls}-date-value`)
                }, toDisplayString(cell.label), 3)
              ], 2))
            ], 42, _hoisted_1))
          ], 64);
        }), 256))
      ], 2);
    }), 128))
  ], 2);
}
var PanelBody = /* @__PURE__ */ _export_sfc(_sfc_main, [["render", _sfc_render]]);
export { PanelBody as default };
