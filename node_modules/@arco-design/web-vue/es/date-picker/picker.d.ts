import { Dayjs } from 'dayjs';
import { PropType } from 'vue';
import { DisabledTimeProps, ShortcutType, FormatFunc, CalendarValue, WeekStart } from './interface';
import { TriggerProps } from '../trigger';
import { TimePickerProps } from '../time-picker/interface';
declare const _default: import("vue").DefineComponent<{
    locale: {
        type: PropType<Record<string, any>>;
    };
    hideTrigger: {
        type: BooleanConstructor;
    };
    allowClear: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
    };
    error: {
        type: BooleanConstructor;
    };
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    shortcuts: {
        type: PropType<ShortcutType[]>;
        default: () => never[];
    };
    shortcutsPosition: {
        type: PropType<"bottom" | "left" | "right">;
        default: string;
    };
    position: {
        type: PropType<"top" | "tl" | "tr" | "bottom" | "bl" | "br">;
        default: string;
    };
    popupVisible: {
        type: BooleanConstructor;
        default: undefined;
    };
    defaultPopupVisible: {
        type: BooleanConstructor;
        default: boolean;
    };
    triggerProps: {
        type: PropType<TriggerProps>;
    };
    unmountOnClose: {
        type: BooleanConstructor;
    };
    placeholder: {
        type: StringConstructor;
    };
    disabled: {
        type: BooleanConstructor;
    };
    disabledDate: {
        type: PropType<(current?: Date | undefined) => boolean>;
    };
    disabledTime: {
        type: PropType<(current: Date) => DisabledTimeProps>;
    };
    pickerValue: {
        type: PropType<string | number | Date>;
    };
    defaultPickerValue: {
        type: PropType<string | number | Date>;
    };
    popupContainer: {
        type: PropType<string | HTMLElement>;
    };
    mode: {
        type: PropType<"year" | "month" | "date" | "week" | "quarter">;
        default: string;
    };
    format: {
        type: PropType<string | FormatFunc>;
    };
    valueFormat: {
        type: PropType<string>;
    };
    previewShortcut: {
        type: BooleanConstructor;
        default: boolean;
    };
    showConfirmBtn: {
        type: BooleanConstructor;
    };
    showTime: {
        type: BooleanConstructor;
    };
    timePickerProps: {
        type: PropType<Partial<TimePickerProps>>;
    };
    showNowBtn: {
        type: BooleanConstructor;
        default: boolean;
    };
    dayStartOfWeek: {
        type: PropType<WeekStart>;
        default: number;
    };
    modelValue: {
        type: PropType<string | number | Date>;
    };
    defaultValue: {
        type: PropType<string | number | Date>;
    };
    disabledInput: {
        type: BooleanConstructor;
        default: boolean;
    };
    abbreviation: {
        type: BooleanConstructor;
        default: boolean;
    };
}, {
    prefixCls: string;
    refInput: import("vue").Ref<any>;
    panelProps: import("vue").ComputedRef<{
        showNowBtn: boolean;
        prefixCls: string;
        format: string;
        value: Dayjs | undefined;
        visible: boolean;
        showConfirmBtn: boolean;
        confirmBtnDisabled: boolean | undefined;
        timePickerProps: {
            visible: boolean;
            popupVisible?: boolean | undefined;
            defaultPopupVisible?: boolean | undefined;
            position?: "top" | "tl" | "tr" | "bottom" | "bl" | "br" | undefined;
            disabled?: boolean | undefined;
            unmountOnClose?: boolean | undefined;
            size?: "mini" | "medium" | "large" | "small" | undefined;
            type?: "time" | "time-range" | undefined;
            readonly?: boolean | undefined;
            error?: boolean | undefined;
            allowClear?: boolean | undefined;
            placeholder?: string | string[] | undefined;
            format: string;
            modelValue?: string | number | {
                toString: () => string;
                toDateString: () => string;
                toTimeString: () => string;
                toLocaleString: {
                    (): string;
                    (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                };
                toLocaleDateString: {
                    (): string;
                    (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                };
                toLocaleTimeString: {
                    (): string;
                    (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                };
                valueOf: () => number;
                getTime: () => number;
                getFullYear: () => number;
                getUTCFullYear: () => number;
                getMonth: () => number;
                getUTCMonth: () => number;
                getDate: () => number;
                getUTCDate: () => number;
                getDay: () => number;
                getUTCDay: () => number;
                getHours: () => number;
                getUTCHours: () => number;
                getMinutes: () => number;
                getUTCMinutes: () => number;
                getSeconds: () => number;
                getUTCSeconds: () => number;
                getMilliseconds: () => number;
                getUTCMilliseconds: () => number;
                getTimezoneOffset: () => number;
                setTime: (time: number) => number;
                setMilliseconds: (ms: number) => number;
                setUTCMilliseconds: (ms: number) => number;
                setSeconds: (sec: number, ms?: number | undefined) => number;
                setUTCSeconds: (sec: number, ms?: number | undefined) => number;
                setMinutes: (min: number, sec?: number | undefined, ms?: number | undefined) => number;
                setUTCMinutes: (min: number, sec?: number | undefined, ms?: number | undefined) => number;
                setHours: (hours: number, min?: number | undefined, sec?: number | undefined, ms?: number | undefined) => number;
                setUTCHours: (hours: number, min?: number | undefined, sec?: number | undefined, ms?: number | undefined) => number;
                setDate: (date: number) => number;
                setUTCDate: (date: number) => number;
                setMonth: (month: number, date?: number | undefined) => number;
                setUTCMonth: (month: number, date?: number | undefined) => number;
                setFullYear: (year: number, month?: number | undefined, date?: number | undefined) => number;
                setUTCFullYear: (year: number, month?: number | undefined, date?: number | undefined) => number;
                toUTCString: () => string;
                toISOString: () => string;
                toJSON: (key?: any) => string;
                getVarDate: () => VarDate;
                [Symbol.toPrimitive]: {
                    (hint: "default"): string;
                    (hint: "string"): string;
                    (hint: "number"): number;
                    (hint: string): string | number;
                };
            } | (string | number | {
                toString: () => string;
                toDateString: () => string;
                toTimeString: () => string;
                toLocaleString: {
                    (): string;
                    (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                };
                toLocaleDateString: {
                    (): string;
                    (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                };
                toLocaleTimeString: {
                    (): string;
                    (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                };
                valueOf: () => number;
                getTime: () => number;
                getFullYear: () => number;
                getUTCFullYear: () => number;
                getMonth: () => number;
                getUTCMonth: () => number;
                getDate: () => number;
                getUTCDate: () => number;
                getDay: () => number;
                getUTCDay: () => number;
                getHours: () => number;
                getUTCHours: () => number;
                getMinutes: () => number;
                getUTCMinutes: () => number;
                getSeconds: () => number;
                getUTCSeconds: () => number;
                getMilliseconds: () => number;
                getUTCMilliseconds: () => number;
                getTimezoneOffset: () => number;
                setTime: (time: number) => number;
                setMilliseconds: (ms: number) => number;
                setUTCMilliseconds: (ms: number) => number;
                setSeconds: (sec: number, ms?: number | undefined) => number;
                setUTCSeconds: (sec: number, ms?: number | undefined) => number;
                setMinutes: (min: number, sec?: number | undefined, ms?: number | undefined) => number;
                setUTCMinutes: (min: number, sec?: number | undefined, ms?: number | undefined) => number;
                setHours: (hours: number, min?: number | undefined, sec?: number | undefined, ms?: number | undefined) => number;
                setUTCHours: (hours: number, min?: number | undefined, sec?: number | undefined, ms?: number | undefined) => number;
                setDate: (date: number) => number;
                setUTCDate: (date: number) => number;
                setMonth: (month: number, date?: number | undefined) => number;
                setUTCMonth: (month: number, date?: number | undefined) => number;
                setFullYear: (year: number, month?: number | undefined, date?: number | undefined) => number;
                setUTCFullYear: (year: number, month?: number | undefined, date?: number | undefined) => number;
                toUTCString: () => string;
                toISOString: () => string;
                toJSON: (key?: any) => string;
                getVarDate: () => VarDate;
                [Symbol.toPrimitive]: {
                    (hint: "default"): string;
                    (hint: "string"): string;
                    (hint: "number"): number;
                    (hint: string): string | number;
                };
            })[] | undefined;
            triggerProps?: {
                [x: string]: unknown;
            } | undefined;
            disableConfirm?: boolean | undefined;
            use12Hours?: boolean | undefined;
            step?: {
                hour?: number | undefined;
                minute?: number | undefined;
                second?: number | undefined;
            } | undefined;
            disabledHours?: (() => number[]) | undefined;
            disabledMinutes?: ((selectedHour?: number | undefined) => number[]) | undefined;
            disabledSeconds?: ((selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]) | undefined;
            hideDisabledOptions?: boolean | undefined;
        };
        extra: import("vue").Slot | undefined;
        dateRender: import("vue").Slot | undefined;
        headerValue: Dayjs;
        headerIcons: {
            prev: import("vue").Slot | undefined;
            prevDouble: import("vue").Slot | undefined;
            next: import("vue").Slot | undefined;
            nextDouble: import("vue").Slot | undefined;
        };
        headerOperations: import("./interface").HeaderOperations;
        timePickerValue: Dayjs;
        headerMode: "year" | "month" | undefined;
        onCellClick: (value: Dayjs) => void;
        onTimePickerSelect: (time: Dayjs) => void;
        onConfirm: () => void;
        onShortcutClick: (value: Dayjs, shortcut: ShortcutType) => void;
        onShortcutMouseEnter: ((value: Dayjs) => void) | undefined;
        onShortcutMouseLeave: (() => void) | undefined;
        onTodayBtnClick: (value: Dayjs) => void;
        onHeaderLabelClick: (type: 'year' | 'month') => void;
        onHeaderSelect: (date: Dayjs) => void;
        onMonthHeaderClick: () => void;
        mode: "year" | "month" | "date" | "week" | "quarter";
        hideTrigger: boolean;
        dayStartOfWeek: WeekStart;
        showTime: boolean;
        disabledDate: ((current?: Date | undefined) => boolean) | undefined;
        disabledTime: ((current: Date) => DisabledTimeProps) | undefined;
        shortcuts: ShortcutType[];
        abbreviation: boolean;
        shortcutsPosition: "bottom" | "left" | "right";
    }>;
    panelValue: import("vue").ComputedRef<Dayjs | undefined>;
    inputValue: import("vue").Ref<string | undefined>;
    selectedValue: import("vue").ComputedRef<Dayjs | undefined>;
    inputFormat: import("vue").ComputedRef<string | ((value: Dayjs) => string)>;
    computedPlaceholder: import("vue").ComputedRef<string>;
    panelVisible: import("vue").ComputedRef<boolean>;
    inputEditable: import("vue").ComputedRef<boolean>;
    needConfirm: import("vue").ComputedRef<boolean>;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    onPanelVisibleChange: (visible: boolean) => void;
    onInputClear: (e: Event) => void;
    onInputChange: (e: any) => void;
    onInputPressEnter: () => void;
    onInputBlur: () => void;
    onPanelClick: () => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    change: (value: CalendarValue | undefined, date: Date | undefined, dateString: string | undefined) => true;
    'update:modelValue': (value: CalendarValue | undefined) => true;
    select: (value: CalendarValue | undefined, date: Date | undefined, dateString: string | undefined) => true;
    'popup-visible-change': (visible: boolean) => true;
    'update:popupVisible': (visible: boolean) => true;
    ok: (value: CalendarValue | undefined, date: Date | undefined, dateString: string | undefined) => true;
    clear: () => true;
    'select-shortcut': (shortcut: ShortcutType) => true;
    'picker-value-change': (value: CalendarValue, date: Date, dateString: string) => true;
    'update:pickerValue': (value: CalendarValue) => true;
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    locale?: unknown;
    hideTrigger?: unknown;
    allowClear?: unknown;
    readonly?: unknown;
    error?: unknown;
    size?: unknown;
    shortcuts?: unknown;
    shortcutsPosition?: unknown;
    position?: unknown;
    popupVisible?: unknown;
    defaultPopupVisible?: unknown;
    triggerProps?: unknown;
    unmountOnClose?: unknown;
    placeholder?: unknown;
    disabled?: unknown;
    disabledDate?: unknown;
    disabledTime?: unknown;
    pickerValue?: unknown;
    defaultPickerValue?: unknown;
    popupContainer?: unknown;
    mode?: unknown;
    format?: unknown;
    valueFormat?: unknown;
    previewShortcut?: unknown;
    showConfirmBtn?: unknown;
    showTime?: unknown;
    timePickerProps?: unknown;
    showNowBtn?: unknown;
    dayStartOfWeek?: unknown;
    modelValue?: unknown;
    defaultValue?: unknown;
    disabledInput?: unknown;
    abbreviation?: unknown;
} & {
    defaultPopupVisible: boolean;
    position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
    disabled: boolean;
    unmountOnClose: boolean;
    mode: "year" | "month" | "date" | "week" | "quarter";
    readonly: boolean;
    error: boolean;
    allowClear: boolean;
    disabledInput: boolean;
    hideTrigger: boolean;
    dayStartOfWeek: WeekStart;
    showTime: boolean;
    showNowBtn: boolean;
    shortcuts: ShortcutType[];
    showConfirmBtn: boolean;
    abbreviation: boolean;
    shortcutsPosition: "bottom" | "left" | "right";
    previewShortcut: boolean;
} & {
    popupVisible?: boolean | undefined;
    popupContainer?: string | HTMLElement | undefined;
    size?: "mini" | "medium" | "large" | "small" | undefined;
    placeholder?: string | undefined;
    format?: string | FormatFunc | undefined;
    defaultValue?: string | number | Date | undefined;
    locale?: Record<string, any> | undefined;
    modelValue?: string | number | Date | undefined;
    triggerProps?: TriggerProps | undefined;
    timePickerProps?: Partial<TimePickerProps> | undefined;
    disabledDate?: ((current?: Date | undefined) => boolean) | undefined;
    disabledTime?: ((current: Date) => DisabledTimeProps) | undefined;
    valueFormat?: string | undefined;
    pickerValue?: string | number | Date | undefined;
    defaultPickerValue?: string | number | Date | undefined;
}> & {
    "onUpdate:popupVisible"?: ((visible: boolean) => any) | undefined;
    onChange?: ((value: CalendarValue | undefined, date: Date | undefined, dateString: string | undefined) => any) | undefined;
    onSelect?: ((value: CalendarValue | undefined, date: Date | undefined, dateString: string | undefined) => any) | undefined;
    onClear?: (() => any) | undefined;
    "onUpdate:modelValue"?: ((value: CalendarValue | undefined) => any) | undefined;
    "onPopup-visible-change"?: ((visible: boolean) => any) | undefined;
    onOk?: ((value: CalendarValue | undefined, date: Date | undefined, dateString: string | undefined) => any) | undefined;
    "onSelect-shortcut"?: ((shortcut: ShortcutType) => any) | undefined;
    "onPicker-value-change"?: ((value: CalendarValue, date: Date, dateString: string) => any) | undefined;
    "onUpdate:pickerValue"?: ((value: CalendarValue) => any) | undefined;
}, {
    popupVisible: boolean;
    defaultPopupVisible: boolean;
    position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
    disabled: boolean;
    unmountOnClose: boolean;
    mode: "year" | "month" | "date" | "week" | "quarter";
    readonly: boolean;
    error: boolean;
    allowClear: boolean;
    disabledInput: boolean;
    hideTrigger: boolean;
    dayStartOfWeek: WeekStart;
    showTime: boolean;
    showNowBtn: boolean;
    shortcuts: ShortcutType[];
    showConfirmBtn: boolean;
    abbreviation: boolean;
    shortcutsPosition: "bottom" | "left" | "right";
    previewShortcut: boolean;
}>;
export default _default;
