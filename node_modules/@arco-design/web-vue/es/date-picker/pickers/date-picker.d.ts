import { PropType } from 'vue';
import { TimePickerProps } from '../../time-picker/interface';
import { DisabledTimeProps, WeekStart } from '../interface';
declare const _default: import("vue").DefineComponent<{
    modelValue: {
        type: PropType<string | number | Date>;
    };
    defaultValue: {
        type: PropType<string | number | Date>;
    };
    format: {
        type: PropType<string | ((current: Date) => string)>;
    };
    dayStartOfWeek: {
        type: PropType<WeekStart>;
        default: number;
    };
    showTime: {
        type: BooleanConstructor;
    };
    timePickerProps: {
        type: PropType<Partial<TimePickerProps>>;
    };
    disabled: {
        type: BooleanConstructor;
    };
    disabledDate: {
        type: PropType<(current?: Date | undefined) => boolean>;
    };
    disabledTime: {
        type: PropType<(current: Date) => DisabledTimeProps>;
    };
    showNowBtn: {
        type: BooleanConstructor;
        default: boolean;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    modelValue?: unknown;
    defaultValue?: unknown;
    format?: unknown;
    dayStartOfWeek?: unknown;
    showTime?: unknown;
    timePickerProps?: unknown;
    disabled?: unknown;
    disabledDate?: unknown;
    disabledTime?: unknown;
    showNowBtn?: unknown;
} & {
    disabled: boolean;
    dayStartOfWeek: WeekStart;
    showTime: boolean;
    showNowBtn: boolean;
} & {
    format?: string | ((current: Date) => string) | undefined;
    defaultValue?: string | number | Date | undefined;
    modelValue?: string | number | Date | undefined;
    timePickerProps?: Partial<TimePickerProps> | undefined;
    disabledDate?: ((current?: Date | undefined) => boolean) | undefined;
    disabledTime?: ((current: Date) => DisabledTimeProps) | undefined;
}>, {
    disabled: boolean;
    dayStartOfWeek: WeekStart;
    showTime: boolean;
    showNowBtn: boolean;
}>;
export default _default;
