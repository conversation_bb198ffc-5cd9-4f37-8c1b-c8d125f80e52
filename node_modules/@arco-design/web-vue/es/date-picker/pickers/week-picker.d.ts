import { PropType } from 'vue';
import { WeekStart } from '../interface';
declare const _default: import("vue").DefineComponent<{
    modelValue: {
        type: PropType<string | number | Date>;
    };
    defaultValue: {
        type: PropType<string | number | Date>;
    };
    format: {
        type: StringConstructor;
        default: string;
    };
    valueFormat: {
        type: StringConstructor;
        default: string;
    };
    dayStartOfWeek: {
        type: PropType<WeekStart>;
        default: number;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    modelValue?: unknown;
    defaultValue?: unknown;
    format?: unknown;
    valueFormat?: unknown;
    dayStartOfWeek?: unknown;
} & {
    format: string;
    dayStartOfWeek: WeekStart;
    valueFormat: string;
} & {
    defaultValue?: string | number | Date | undefined;
    modelValue?: string | number | Date | undefined;
}>, {
    format: string;
    dayStartOfWeek: WeekStart;
    valueFormat: string;
}>;
export default _default;
