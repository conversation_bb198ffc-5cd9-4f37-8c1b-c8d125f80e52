import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<{
    modelValue: {
        type: PropType<string | number | Date>;
    };
    defaultValue: {
        type: PropType<string | number | Date>;
    };
    format: {
        type: StringConstructor;
        default: string;
    };
    valueFormat: {
        type: StringConstructor;
        default: string;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    modelValue?: unknown;
    defaultValue?: unknown;
    format?: unknown;
    valueFormat?: unknown;
} & {
    format: string;
    valueFormat: string;
} & {
    defaultValue?: string | number | Date | undefined;
    modelValue?: string | number | Date | undefined;
}>, {
    format: string;
    valueFormat: string;
}>;
export default _default;
