import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _DatePicker from './pickers/date-picker';
import _WeekPicker from './pickers/week-picker';
import _MonthPicker from './pickers/month-picker';
import _YearPicker from './pickers/year-picker';
import _QuarterPicker from './pickers/quarter-picker';
import _RangePicker from './range-picker';
export type { ShortcutType } from './interface';
declare const DatePicker: {
    new (...args: any[]): {
        $: import("vue").ComponentInternalInstance;
        $data: {};
        $props: Partial<{
            disabled: boolean;
            dayStartOfWeek: import("./interface").WeekStart;
            showTime: boolean;
            showNowBtn: boolean;
        }> & Omit<Readonly<{
            modelValue?: unknown;
            defaultValue?: unknown;
            format?: unknown;
            dayStartOfWeek?: unknown;
            showTime?: unknown;
            timePickerProps?: unknown;
            disabled?: unknown;
            disabledDate?: unknown;
            disabledTime?: unknown;
            showNowBtn?: unknown;
        } & {
            disabled: boolean;
            dayStartOfWeek: import("./interface").WeekStart;
            showTime: boolean;
            showNowBtn: boolean;
        } & {
            format?: string | ((current: Date) => string) | undefined;
            defaultValue?: string | number | Date | undefined;
            modelValue?: string | number | Date | undefined;
            timePickerProps?: Partial<import("../time-picker/interface").TimePickerProps> | undefined;
            disabledDate?: ((current?: Date | undefined) => boolean) | undefined;
            disabledTime?: ((current: Date) => import("./interface").DisabledTimeProps) | undefined;
        }> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, "disabled" | "dayStartOfWeek" | "showTime" | "showNowBtn">;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        };
        $slots: Readonly<{
            [name: string]: import("vue").Slot | undefined;
        }>;
        $root: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $parent: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $emit: (event: string, ...args: any[]) => void;
        $el: any;
        $options: import("vue").ComponentOptionsBase<Readonly<{
            modelValue?: unknown;
            defaultValue?: unknown;
            format?: unknown;
            dayStartOfWeek?: unknown;
            showTime?: unknown;
            timePickerProps?: unknown;
            disabled?: unknown;
            disabledDate?: unknown;
            disabledTime?: unknown;
            showNowBtn?: unknown;
        } & {
            disabled: boolean;
            dayStartOfWeek: import("./interface").WeekStart;
            showTime: boolean;
            showNowBtn: boolean;
        } & {
            format?: string | ((current: Date) => string) | undefined;
            defaultValue?: string | number | Date | undefined;
            modelValue?: string | number | Date | undefined;
            timePickerProps?: Partial<import("../time-picker/interface").TimePickerProps> | undefined;
            disabledDate?: ((current?: Date | undefined) => boolean) | undefined;
            disabledTime?: ((current: Date) => import("./interface").DisabledTimeProps) | undefined;
        }>, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
            disabled: boolean;
            dayStartOfWeek: import("./interface").WeekStart;
            showTime: boolean;
            showNowBtn: boolean;
        }> & {
            beforeCreate?: ((() => void) | (() => void)[]) | undefined;
            created?: ((() => void) | (() => void)[]) | undefined;
            beforeMount?: ((() => void) | (() => void)[]) | undefined;
            mounted?: ((() => void) | (() => void)[]) | undefined;
            beforeUpdate?: ((() => void) | (() => void)[]) | undefined;
            updated?: ((() => void) | (() => void)[]) | undefined;
            activated?: ((() => void) | (() => void)[]) | undefined;
            deactivated?: ((() => void) | (() => void)[]) | undefined;
            beforeDestroy?: ((() => void) | (() => void)[]) | undefined;
            beforeUnmount?: ((() => void) | (() => void)[]) | undefined;
            destroyed?: ((() => void) | (() => void)[]) | undefined;
            unmounted?: ((() => void) | (() => void)[]) | undefined;
            renderTracked?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            renderTriggered?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            errorCaptured?: (((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void) | ((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void)[]) | undefined;
        };
        $forceUpdate: () => void;
        $nextTick: typeof import("vue").nextTick;
        $watch(source: string | Function, cb: Function, options?: import("vue").WatchOptions<boolean> | undefined): import("vue").WatchStopHandle;
    } & Readonly<{
        modelValue?: unknown;
        defaultValue?: unknown;
        format?: unknown;
        dayStartOfWeek?: unknown;
        showTime?: unknown;
        timePickerProps?: unknown;
        disabled?: unknown;
        disabledDate?: unknown;
        disabledTime?: unknown;
        showNowBtn?: unknown;
    } & {
        disabled: boolean;
        dayStartOfWeek: import("./interface").WeekStart;
        showTime: boolean;
        showNowBtn: boolean;
    } & {
        format?: string | ((current: Date) => string) | undefined;
        defaultValue?: string | number | Date | undefined;
        modelValue?: string | number | Date | undefined;
        timePickerProps?: Partial<import("../time-picker/interface").TimePickerProps> | undefined;
        disabledDate?: ((current?: Date | undefined) => boolean) | undefined;
        disabledTime?: ((current: Date) => import("./interface").DisabledTimeProps) | undefined;
    }> & import("vue").ShallowUnwrapRef<() => JSX.Element> & {} & {} & import("vue").ComponentCustomProperties;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<{
    modelValue?: unknown;
    defaultValue?: unknown;
    format?: unknown;
    dayStartOfWeek?: unknown;
    showTime?: unknown;
    timePickerProps?: unknown;
    disabled?: unknown;
    disabledDate?: unknown;
    disabledTime?: unknown;
    showNowBtn?: unknown;
} & {
    disabled: boolean;
    dayStartOfWeek: import("./interface").WeekStart;
    showTime: boolean;
    showNowBtn: boolean;
} & {
    format?: string | ((current: Date) => string) | undefined;
    defaultValue?: string | number | Date | undefined;
    modelValue?: string | number | Date | undefined;
    timePickerProps?: Partial<import("../time-picker/interface").TimePickerProps> | undefined;
    disabledDate?: ((current?: Date | undefined) => boolean) | undefined;
    disabledTime?: ((current: Date) => import("./interface").DisabledTimeProps) | undefined;
}>, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    disabled: boolean;
    dayStartOfWeek: import("./interface").WeekStart;
    showTime: boolean;
    showNowBtn: boolean;
}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    WeekPicker: import("vue").DefineComponent<{
        modelValue: {
            type: import("vue").PropType<string | number | Date>;
        };
        defaultValue: {
            type: import("vue").PropType<string | number | Date>;
        };
        format: {
            type: StringConstructor;
            default: string;
        };
        valueFormat: {
            type: StringConstructor;
            default: string;
        };
        dayStartOfWeek: {
            type: import("vue").PropType<import("./interface").WeekStart>;
            default: number;
        };
    }, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
        modelValue?: unknown;
        defaultValue?: unknown;
        format?: unknown;
        valueFormat?: unknown;
        dayStartOfWeek?: unknown;
    } & {
        format: string;
        dayStartOfWeek: import("./interface").WeekStart;
        valueFormat: string;
    } & {
        defaultValue?: string | number | Date | undefined;
        modelValue?: string | number | Date | undefined;
    }>, {
        format: string;
        dayStartOfWeek: import("./interface").WeekStart;
        valueFormat: string;
    }>;
    MonthPicker: import("vue").DefineComponent<{
        modelValue: {
            type: import("vue").PropType<string | number | Date>;
        };
        defaultValue: {
            type: import("vue").PropType<string | number | Date>;
        };
        format: {
            type: StringConstructor;
            default: string;
        };
    }, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
        modelValue?: unknown;
        defaultValue?: unknown;
        format?: unknown;
    } & {
        format: string;
    } & {
        defaultValue?: string | number | Date | undefined;
        modelValue?: string | number | Date | undefined;
    }>, {
        format: string;
    }>;
    YearPicker: import("vue").DefineComponent<{
        modelValue: {
            type: import("vue").PropType<string | number | Date>;
        };
        defaultValue: {
            type: import("vue").PropType<string | number | Date>;
        };
        format: {
            type: StringConstructor;
            default: string;
        };
    }, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
        modelValue?: unknown;
        defaultValue?: unknown;
        format?: unknown;
    } & {
        format: string;
    } & {
        defaultValue?: string | number | Date | undefined;
        modelValue?: string | number | Date | undefined;
    }>, {
        format: string;
    }>;
    QuarterPicker: import("vue").DefineComponent<{
        modelValue: {
            type: import("vue").PropType<string | number | Date>;
        };
        defaultValue: {
            type: import("vue").PropType<string | number | Date>;
        };
        format: {
            type: StringConstructor;
            default: string;
        };
        valueFormat: {
            type: StringConstructor;
            default: string;
        };
    }, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
        modelValue?: unknown;
        defaultValue?: unknown;
        format?: unknown;
        valueFormat?: unknown;
    } & {
        format: string;
        valueFormat: string;
    } & {
        defaultValue?: string | number | Date | undefined;
        modelValue?: string | number | Date | undefined;
    }>, {
        format: string;
        valueFormat: string;
    }>;
    RangePicker: import("vue").DefineComponent<{
        mode: {
            type: import("vue").PropType<"year" | "month" | "date" | "week" | "quarter">;
            default: string;
        };
        modelValue: {
            type: import("vue").PropType<(string | number | Date)[]>;
        };
        defaultValue: {
            type: import("vue").PropType<(string | number | Date)[]>;
        };
        pickerValue: {
            type: import("vue").PropType<(string | number | Date)[]>;
        };
        defaultPickerValue: {
            type: import("vue").PropType<(string | number | Date)[]>;
        };
        disabled: {
            type: import("vue").PropType<boolean | boolean[]>;
            default: boolean;
        };
        dayStartOfWeek: {
            type: import("vue").PropType<import("./interface").WeekStart>;
            default: number;
        };
        format: {
            type: StringConstructor;
        };
        valueFormat: {
            type: import("vue").PropType<string>;
        };
        showTime: {
            type: BooleanConstructor;
        };
        timePickerProps: {
            type: import("vue").PropType<Partial<import("../time-picker/interface").TimePickerProps>>;
        };
        placeholder: {
            type: import("vue").PropType<string[]>;
        };
        disabledDate: {
            type: import("vue").PropType<(current: Date, type: "end" | "start") => boolean>;
        };
        disabledTime: {
            type: import("vue").PropType<(current: Date, type: "end" | "start") => import("./interface").DisabledTimeProps>;
        };
        separator: {
            type: StringConstructor;
        };
        exchangeTime: {
            type: BooleanConstructor;
            default: boolean;
        };
        popupContainer: {
            type: import("vue").PropType<string | HTMLElement>;
        };
        locale: {
            type: import("vue").PropType<Record<string, any>>;
        };
        hideTrigger: {
            type: BooleanConstructor;
        };
        allowClear: {
            type: BooleanConstructor;
            default: boolean;
        };
        readonly: {
            type: BooleanConstructor;
        };
        error: {
            type: BooleanConstructor;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
        };
        shortcuts: {
            type: import("vue").PropType<import("./interface").ShortcutType[]>;
            default: () => never[];
        };
        shortcutsPosition: {
            type: import("vue").PropType<"bottom" | "left" | "right">;
            default: string;
        };
        position: {
            type: import("vue").PropType<"top" | "tl" | "tr" | "bottom" | "bl" | "br">;
            default: string;
        };
        popupVisible: {
            type: BooleanConstructor;
            default: undefined;
        };
        defaultPopupVisible: {
            type: BooleanConstructor;
        };
        triggerProps: {
            type: import("vue").PropType<import("..").TriggerProps>;
        };
        unmountOnClose: {
            type: BooleanConstructor;
        };
        previewShortcut: {
            type: BooleanConstructor;
            default: boolean;
        };
        showConfirmBtn: {
            type: BooleanConstructor;
        };
        disabledInput: {
            type: BooleanConstructor;
            default: boolean;
        };
        abbreviation: {
            type: BooleanConstructor;
            default: boolean;
        };
    }, {
        prefixCls: string;
        refInput: import("vue").Ref<any>;
        computedFormat: import("vue").ComputedRef<string>;
        computedPlaceholder: import("vue").ComputedRef<string[]>;
        panelVisible: import("vue").ComputedRef<boolean>;
        panelValue: import("vue").ComputedRef<(import("dayjs").Dayjs | undefined)[]>;
        inputValue: import("vue").Ref<(string | undefined)[] | undefined>;
        focusedIndex: import("vue").Ref<number>;
        triggerDisabled: import("vue").ComputedRef<boolean>;
        mergedSize: import("vue").ComputedRef<"mini" | "medium" | "large" | "small" | undefined>;
        mergedError: import("vue").ComputedRef<boolean>;
        onPanelVisibleChange: (visible: boolean) => void;
        onInputClear: (e: Event) => void;
        onInputChange: (e: any) => void;
        onInputPressEnter: () => void;
        rangePanelProps: import("vue").ComputedRef<{
            prefixCls: string;
            format: string;
            value: (import("dayjs").Dayjs | undefined)[];
            showConfirmBtn: boolean;
            confirmBtnDisabled: boolean | undefined;
            timePickerValue: import("dayjs").Dayjs[];
            timePickerProps: {
                visible: boolean;
                popupVisible?: boolean | undefined;
                defaultPopupVisible?: boolean | undefined;
                position?: "top" | "tl" | "tr" | "bottom" | "bl" | "br" | undefined;
                disabled?: boolean | undefined;
                unmountOnClose?: boolean | undefined;
                size?: "mini" | "medium" | "large" | "small" | undefined;
                type?: "time" | "time-range" | undefined;
                readonly?: boolean | undefined;
                error?: boolean | undefined;
                allowClear?: boolean | undefined;
                placeholder?: string | string[] | undefined;
                format: string;
                modelValue?: string | number | {
                    toString: () => string;
                    toDateString: () => string;
                    toTimeString: () => string;
                    toLocaleString: {
                        (): string;
                        (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                    };
                    toLocaleDateString: {
                        (): string;
                        (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                    };
                    toLocaleTimeString: {
                        (): string;
                        (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                    };
                    valueOf: () => number;
                    getTime: () => number;
                    getFullYear: () => number;
                    getUTCFullYear: () => number;
                    getMonth: () => number;
                    getUTCMonth: () => number;
                    getDate: () => number;
                    getUTCDate: () => number;
                    getDay: () => number;
                    getUTCDay: () => number;
                    getHours: () => number;
                    getUTCHours: () => number;
                    getMinutes: () => number;
                    getUTCMinutes: () => number;
                    getSeconds: () => number;
                    getUTCSeconds: () => number;
                    getMilliseconds: () => number;
                    getUTCMilliseconds: () => number;
                    getTimezoneOffset: () => number;
                    setTime: (time: number) => number;
                    setMilliseconds: (ms: number) => number;
                    setUTCMilliseconds: (ms: number) => number;
                    setSeconds: (sec: number, ms?: number | undefined) => number;
                    setUTCSeconds: (sec: number, ms?: number | undefined) => number;
                    setMinutes: (min: number, sec?: number | undefined, ms?: number | undefined) => number;
                    setUTCMinutes: (min: number, sec?: number | undefined, ms?: number | undefined) => number;
                    setHours: (hours: number, min?: number | undefined, sec?: number | undefined, ms?: number | undefined) => number;
                    setUTCHours: (hours: number, min?: number | undefined, sec?: number | undefined, ms?: number | undefined) => number;
                    setDate: (date: number) => number;
                    setUTCDate: (date: number) => number;
                    setMonth: (month: number, date?: number | undefined) => number;
                    setUTCMonth: (month: number, date?: number | undefined) => number;
                    setFullYear: (year: number, month?: number | undefined, date?: number | undefined) => number;
                    setUTCFullYear: (year: number, month?: number | undefined, date?: number | undefined) => number;
                    toUTCString: () => string;
                    toISOString: () => string;
                    toJSON: (key?: any) => string;
                    getVarDate: () => VarDate;
                    [Symbol.toPrimitive]: {
                        (hint: "default"): string;
                        (hint: "string"): string;
                        (hint: "number"): number;
                        (hint: string): string | number;
                    };
                } | (string | number | {
                    toString: () => string;
                    toDateString: () => string;
                    toTimeString: () => string;
                    toLocaleString: {
                        (): string;
                        (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                    };
                    toLocaleDateString: {
                        (): string;
                        (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                    };
                    toLocaleTimeString: {
                        (): string;
                        (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                    };
                    valueOf: () => number;
                    getTime: () => number;
                    getFullYear: () => number;
                    getUTCFullYear: () => number;
                    getMonth: () => number;
                    getUTCMonth: () => number;
                    getDate: () => number;
                    getUTCDate: () => number;
                    getDay: () => number;
                    getUTCDay: () => number;
                    getHours: () => number;
                    getUTCHours: () => number;
                    getMinutes: () => number;
                    getUTCMinutes: () => number;
                    getSeconds: () => number;
                    getUTCSeconds: () => number;
                    getMilliseconds: () => number;
                    getUTCMilliseconds: () => number;
                    getTimezoneOffset: () => number;
                    setTime: (time: number) => number;
                    setMilliseconds: (ms: number) => number;
                    setUTCMilliseconds: (ms: number) => number;
                    setSeconds: (sec: number, ms?: number | undefined) => number;
                    setUTCSeconds: (sec: number, ms?: number | undefined) => number;
                    setMinutes: (min: number, sec?: number | undefined, ms?: number | undefined) => number;
                    setUTCMinutes: (min: number, sec?: number | undefined, ms?: number | undefined) => number;
                    setHours: (hours: number, min?: number | undefined, sec?: number | undefined, ms?: number | undefined) => number;
                    setUTCHours: (hours: number, min?: number | undefined, sec?: number | undefined, ms?: number | undefined) => number;
                    setDate: (date: number) => number;
                    setUTCDate: (date: number) => number;
                    setMonth: (month: number, date?: number | undefined) => number;
                    setUTCMonth: (month: number, date?: number | undefined) => number;
                    setFullYear: (year: number, month?: number | undefined, date?: number | undefined) => number;
                    setUTCFullYear: (year: number, month?: number | undefined, date?: number | undefined) => number;
                    toUTCString: () => string;
                    toISOString: () => string;
                    toJSON: (key?: any) => string;
                    getVarDate: () => VarDate;
                    [Symbol.toPrimitive]: {
                        (hint: "default"): string;
                        (hint: "string"): string;
                        (hint: "number"): number;
                        (hint: string): string | number;
                    };
                })[] | undefined;
                triggerProps?: {
                    [x: string]: unknown;
                } | undefined;
                disableConfirm?: boolean | undefined;
                use12Hours?: boolean | undefined;
                step?: {
                    hour?: number | undefined;
                    minute?: number | undefined;
                    second?: number | undefined;
                } | undefined;
                disabledHours?: (() => number[]) | undefined;
                disabledMinutes?: ((selectedHour?: number | undefined) => number[]) | undefined;
                disabledSeconds?: ((selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]) | undefined;
                hideDisabledOptions?: boolean | undefined;
            };
            extra: import("vue").Slot | undefined;
            dateRender: import("vue").Slot | undefined;
            startHeaderProps: {
                headerValue: import("dayjs").Dayjs;
                headerOperations: Pick<any, string>;
                headerIcons: {
                    prev: import("vue").Slot | undefined;
                    prevDouble: import("vue").Slot | undefined;
                    next: import("vue").Slot | undefined;
                    nextDouble: import("vue").Slot | undefined;
                };
            };
            endHeaderProps: {
                headerValue: import("dayjs").Dayjs;
                headerOperations: Pick<any, string>;
                headerIcons: {
                    prev: import("vue").Slot | undefined;
                    prevDouble: import("vue").Slot | undefined;
                    next: import("vue").Slot | undefined;
                    nextDouble: import("vue").Slot | undefined;
                };
            };
            footerValue: {
                clone: () => import("dayjs").Dayjs;
                isValid: () => boolean;
                year: {
                    (): number;
                    (value: number): import("dayjs").Dayjs;
                };
                month: {
                    (): number;
                    (value: number): import("dayjs").Dayjs;
                };
                date: {
                    (): number;
                    (value: number): import("dayjs").Dayjs;
                };
                day: {
                    (): number;
                    (value: number): import("dayjs").Dayjs;
                };
                hour: {
                    (): number;
                    (value: number): import("dayjs").Dayjs;
                };
                minute: {
                    (): number;
                    (value: number): import("dayjs").Dayjs;
                };
                second: {
                    (): number;
                    (value: number): import("dayjs").Dayjs;
                };
                millisecond: {
                    (): number;
                    (value: number): import("dayjs").Dayjs;
                };
                set: (unit: import("dayjs").UnitType, value: number) => import("dayjs").Dayjs;
                get: (unit: import("dayjs").UnitType) => number;
                add: {
                    (value: number, unit?: import("dayjs").ManipulateType | undefined): import("dayjs").Dayjs;
                    (value: number, unit: import("dayjs").QUnitType): import("dayjs").Dayjs;
                };
                subtract: {
                    (value: number, unit?: import("dayjs").ManipulateType | undefined): import("dayjs").Dayjs;
                    (value: number, unit: import("dayjs").QUnitType): import("dayjs").Dayjs;
                };
                startOf: {
                    (unit: import("dayjs").OpUnitType): import("dayjs").Dayjs;
                    (unit: import("dayjs").QUnitType): import("dayjs").Dayjs;
                };
                endOf: {
                    (unit: import("dayjs").OpUnitType): import("dayjs").Dayjs;
                    (unit: import("dayjs").QUnitType): import("dayjs").Dayjs;
                };
                format: (template?: string | undefined) => string;
                diff: (date?: string | number | Date | import("dayjs").Dayjs | null | undefined, unit?: "M" | "y" | "s" | "year" | "month" | "date" | "day" | "hour" | "minute" | "second" | "millisecond" | "week" | "quarter" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "weeks" | "w" | "quarters" | "Q" | undefined, float?: boolean | undefined) => number;
                valueOf: () => number;
                unix: () => number;
                daysInMonth: () => number;
                toDate: () => Date;
                toJSON: () => string;
                toISOString: () => string;
                toString: () => string;
                utcOffset: () => number;
                isBefore: {
                    (date: string | number | Date | import("dayjs").Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                    (date: string | number | Date | import("dayjs").Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                };
                isSame: {
                    (date: string | number | Date | import("dayjs").Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                    (date: string | number | Date | import("dayjs").Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                };
                isAfter: {
                    (date: string | number | Date | import("dayjs").Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                    (date: string | number | Date | import("dayjs").Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
                };
                locale: {
                    (): string;
                    (preset: string | ILocale, object?: Partial<ILocale> | undefined): import("dayjs").Dayjs;
                };
                isBetween: (a: string | number | Date | import("dayjs").Dayjs | null | undefined, b: string | number | Date | import("dayjs").Dayjs | null | undefined, c?: import("dayjs").OpUnitType | null | undefined, d?: "()" | "[]" | "[)" | "(]" | undefined) => boolean;
                week: {
                    (): number;
                    (value: number): import("dayjs").Dayjs;
                };
                weekYear: () => number;
                quarter: {
                    (): number;
                    (quarter: number): import("dayjs").Dayjs;
                };
            }[];
            disabled: boolean[];
            visible: boolean;
            onCellClick: (date: import("dayjs").Dayjs) => void;
            onCellMouseEnter: (date: import("dayjs").Dayjs) => void;
            onShortcutClick: (value: (import("dayjs").Dayjs | undefined)[], shortcut: import("./interface").ShortcutType) => void;
            onShortcutMouseEnter: ((value: (import("dayjs").Dayjs | undefined)[]) => void) | undefined;
            onShortcutMouseLeave: (() => void) | undefined;
            onConfirm: () => void;
            onTimePickerSelect: (time: import("dayjs").Dayjs, type: "end" | "start") => void;
            startHeaderMode: "year" | "month" | undefined;
            endHeaderMode: "year" | "month" | undefined;
            onStartHeaderLabelClick: (type: "year" | "month") => void;
            onEndHeaderLabelClick: (type: "year" | "month") => void;
            onStartHeaderSelect: (date: import("dayjs").Dayjs) => void;
            onEndHeaderSelect: (date: import("dayjs").Dayjs) => void;
            mode: "year" | "month" | "date" | "week" | "quarter";
            hideTrigger: boolean;
            dayStartOfWeek: import("./interface").WeekStart;
            showTime: boolean;
            disabledDate: ((current: Date, type: "end" | "start") => boolean) | undefined;
            disabledTime: ((current: Date, type: "end" | "start") => import("./interface").DisabledTimeProps) | undefined;
            shortcuts: import("./interface").ShortcutType[];
            abbreviation: boolean;
            shortcutsPosition: "bottom" | "left" | "right";
        }>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        change: (value: (import("./interface").CalendarValue | undefined)[] | undefined, date: (Date | undefined)[] | undefined, dateString: (string | undefined)[] | undefined) => true;
        'update:modelValue': (value: (import("./interface").CalendarValue | undefined)[] | undefined) => true;
        select: (value: (import("./interface").CalendarValue | undefined)[], date: (Date | undefined)[], dateString: (string | undefined)[]) => true;
        'popup-visible-change': (visible: boolean) => true;
        'update:popupVisible': (visible: boolean) => true;
        ok: (value: import("./interface").CalendarValue[], date: Date[], dateString: string[]) => true;
        clear: () => true;
        'select-shortcut': (shortcut: import("./interface").ShortcutType) => true;
        'picker-value-change': (value: import("./interface").CalendarValue[], date: Date[], dateString: string[]) => true;
        'update:pickerValue': (value: import("./interface").CalendarValue[]) => true;
    }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
        mode?: unknown;
        modelValue?: unknown;
        defaultValue?: unknown;
        pickerValue?: unknown;
        defaultPickerValue?: unknown;
        disabled?: unknown;
        dayStartOfWeek?: unknown;
        format?: unknown;
        valueFormat?: unknown;
        showTime?: unknown;
        timePickerProps?: unknown;
        placeholder?: unknown;
        disabledDate?: unknown;
        disabledTime?: unknown;
        separator?: unknown;
        exchangeTime?: unknown;
        popupContainer?: unknown;
        locale?: unknown;
        hideTrigger?: unknown;
        allowClear?: unknown;
        readonly?: unknown;
        error?: unknown;
        size?: unknown;
        shortcuts?: unknown;
        shortcutsPosition?: unknown;
        position?: unknown;
        popupVisible?: unknown;
        defaultPopupVisible?: unknown;
        triggerProps?: unknown;
        unmountOnClose?: unknown;
        previewShortcut?: unknown;
        showConfirmBtn?: unknown;
        disabledInput?: unknown;
        abbreviation?: unknown;
    } & {
        defaultPopupVisible: boolean;
        position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
        disabled: boolean | boolean[];
        unmountOnClose: boolean;
        mode: "year" | "month" | "date" | "week" | "quarter";
        readonly: boolean;
        error: boolean;
        allowClear: boolean;
        disabledInput: boolean;
        hideTrigger: boolean;
        dayStartOfWeek: import("./interface").WeekStart;
        showTime: boolean;
        shortcuts: import("./interface").ShortcutType[];
        showConfirmBtn: boolean;
        abbreviation: boolean;
        shortcutsPosition: "bottom" | "left" | "right";
        exchangeTime: boolean;
        previewShortcut: boolean;
    } & {
        popupVisible?: boolean | undefined;
        popupContainer?: string | HTMLElement | undefined;
        size?: "mini" | "medium" | "large" | "small" | undefined;
        separator?: string | undefined;
        placeholder?: string[] | undefined;
        format?: string | undefined;
        defaultValue?: (string | number | Date)[] | undefined;
        locale?: Record<string, any> | undefined;
        modelValue?: (string | number | Date)[] | undefined;
        triggerProps?: import("..").TriggerProps | undefined;
        timePickerProps?: Partial<import("../time-picker/interface").TimePickerProps> | undefined;
        disabledDate?: ((current: Date, type: "end" | "start") => boolean) | undefined;
        disabledTime?: ((current: Date, type: "end" | "start") => import("./interface").DisabledTimeProps) | undefined;
        valueFormat?: string | undefined;
        pickerValue?: (string | number | Date)[] | undefined;
        defaultPickerValue?: (string | number | Date)[] | undefined;
    }> & {
        "onUpdate:popupVisible"?: ((visible: boolean) => any) | undefined;
        onChange?: ((value: (import("./interface").CalendarValue | undefined)[] | undefined, date: (Date | undefined)[] | undefined, dateString: (string | undefined)[] | undefined) => any) | undefined;
        onSelect?: ((value: (import("./interface").CalendarValue | undefined)[], date: (Date | undefined)[], dateString: (string | undefined)[]) => any) | undefined;
        onClear?: (() => any) | undefined;
        "onUpdate:modelValue"?: ((value: (import("./interface").CalendarValue | undefined)[] | undefined) => any) | undefined;
        "onPopup-visible-change"?: ((visible: boolean) => any) | undefined;
        onOk?: ((value: import("./interface").CalendarValue[], date: Date[], dateString: string[]) => any) | undefined;
        "onSelect-shortcut"?: ((shortcut: import("./interface").ShortcutType) => any) | undefined;
        "onPicker-value-change"?: ((value: import("./interface").CalendarValue[], date: Date[], dateString: string[]) => any) | undefined;
        "onUpdate:pickerValue"?: ((value: import("./interface").CalendarValue[]) => any) | undefined;
    }, {
        popupVisible: boolean;
        defaultPopupVisible: boolean;
        position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
        disabled: boolean | boolean[];
        unmountOnClose: boolean;
        mode: "year" | "month" | "date" | "week" | "quarter";
        readonly: boolean;
        error: boolean;
        allowClear: boolean;
        disabledInput: boolean;
        hideTrigger: boolean;
        dayStartOfWeek: import("./interface").WeekStart;
        showTime: boolean;
        shortcuts: import("./interface").ShortcutType[];
        showConfirmBtn: boolean;
        abbreviation: boolean;
        shortcutsPosition: "bottom" | "left" | "right";
        exchangeTime: boolean;
        previewShortcut: boolean;
    }>;
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type DatePickerInstance = InstanceType<typeof _DatePicker>;
export declare type WeekPickerInstance = InstanceType<typeof _WeekPicker>;
export declare type MonthPickerInstance = InstanceType<typeof _MonthPicker>;
export declare type YearPickerInstance = InstanceType<typeof _YearPicker>;
export declare type QuarterPickerInstance = InstanceType<typeof _QuarterPicker>;
export declare type RangePickerInstance = InstanceType<typeof _RangePicker>;
export { _WeekPicker as WeekPicker, _MonthPicker as MonthPicker, _YearPicker as YearPicker, _QuarterPicker as QuarterPicker, _RangePicker as RangePicker, };
export default DatePicker;
