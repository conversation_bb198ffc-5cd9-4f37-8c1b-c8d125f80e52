import { defineComponent, toRefs, computed, reactive, watch, resolveComponent, openBlock, createElementBlock, normalizeClass, createBlock, normalizeProps, mergeProps, createCommentVNode, createElementVNode, Fragment, createVNode, createSlots, withCtx, guardReactiveProps } from "vue";
import { isFunction } from "../_utils/is.js";
import { getNow, getDayjsValue } from "../_utils/date.js";
import PanelShortcuts from "./panels/shortcuts.js";
import DatePanel from "./panels/date/index.js";
import WeekPanel from "./panels/week/index.js";
import MonthPanel from "./panels/month/index.js";
import YearPanel from "./panels/year/index.js";
import QuarterPanel from "./panels/quarter/index.js";
import PanelFooter from "./panels/footer.js";
import RenderFunction from "../_components/render-function.js";
import useHeaderValue from "./hooks/use-header-value.js";
import _export_sfc from "../_virtual/plugin-vue_export-helper.js";
const _sfc_main = defineComponent({
  name: "DatePikerPanel",
  components: {
    DatePanel,
    PanelShortcuts,
    PanelFooter,
    WeekPanel,
    MonthPanel,
    YearPanel,
    QuarterPanel,
    RenderFunction
  },
  props: {
    mode: {
      type: String
    },
    headerMode: {
      type: String
    },
    prefixCls: {
      type: String,
      required: true
    },
    value: {
      type: Object
    },
    headerValue: {
      type: Object,
      required: true
    },
    timePickerValue: {
      type: Object
    },
    showTime: {
      type: Boolean
    },
    showConfirmBtn: {
      type: Boolean
    },
    shortcuts: {
      type: Array,
      default: () => []
    },
    shortcutsPosition: {
      type: String,
      default: "bottom"
    },
    format: {
      type: String,
      required: true
    },
    dayStartOfWeek: {
      type: Number,
      default: 0
    },
    disabledDate: {
      type: Function
    },
    disabledTime: {
      type: Function
    },
    timePickerProps: {
      type: Object
    },
    extra: {
      type: Function
    },
    dateRender: {
      type: Function
    },
    hideTrigger: {
      type: Boolean
    },
    confirmBtnDisabled: {
      type: Boolean
    },
    showNowBtn: {
      type: Boolean
    },
    headerIcons: {
      type: Object,
      default: () => ({})
    },
    headerOperations: {
      type: Object
    },
    abbreviation: {
      type: Boolean
    }
  },
  emits: [
    "cell-click",
    "time-picker-select",
    "shortcut-click",
    "shortcut-mouse-enter",
    "shortcut-mouse-leave",
    "confirm",
    "today-btn-click",
    "header-label-click",
    "header-select",
    "month-header-click"
  ],
  setup(props, { emit }) {
    const {
      prefixCls,
      shortcuts,
      shortcutsPosition,
      format,
      value,
      disabledDate,
      hideTrigger,
      showNowBtn,
      dateRender,
      showConfirmBtn,
      headerValue,
      headerIcons,
      headerOperations,
      headerMode
    } = toRefs(props);
    const hasShortcuts = computed(() => Boolean(shortcuts.value && shortcuts.value.length));
    const showShortcutsNowBtn = computed(() => showNowBtn.value && showConfirmBtn.value && !hasShortcuts.value);
    const showShortcuts = computed(() => showShortcutsNowBtn.value || hasShortcuts.value);
    const showShortcutsInLeft = computed(() => showShortcuts.value && shortcutsPosition.value === "left");
    const showShortcutsInRight = computed(() => showShortcuts.value && shortcutsPosition.value === "right");
    const showShortcutsInBottom = computed(() => showShortcuts.value && shortcutsPosition.value === "bottom");
    const classNames = computed(() => [
      `${prefixCls.value}-container`,
      {
        [`${prefixCls.value}-container-panel-only`]: hideTrigger.value,
        [`${prefixCls.value}-container-shortcuts-placement-left`]: showShortcutsInLeft.value,
        [`${prefixCls.value}-container-shortcuts-placement-right`]: showShortcutsInRight.value
      }
    ]);
    const footerValue = computed(() => (value == null ? void 0 : value.value) || getNow());
    const {
      headerValue: headerPanelHeaderValue,
      setHeaderValue: setHeaderPanelHeaderValue,
      headerOperations: headerPanelHeaderOperations
    } = useHeaderValue(reactive({
      mode: headerMode,
      format
    }));
    watch(headerValue, (val) => {
      setHeaderPanelHeaderValue(val);
    });
    function getShortcutValue(shortcut) {
      const { value: value2 } = shortcut;
      return getDayjsValue(isFunction(value2) ? value2() : value2, shortcut.format || format.value);
    }
    function onShortcutClick(shortcut) {
      emit("shortcut-click", getShortcutValue(shortcut), shortcut);
    }
    function onShortcutMouseEnter(shortcut) {
      emit("shortcut-mouse-enter", getShortcutValue(shortcut));
    }
    function onShortcutMouseLeave(shortcut) {
      emit("shortcut-mouse-leave", getShortcutValue(shortcut));
    }
    function onPanelSelect(date) {
      emit("cell-click", date);
    }
    function onTimePickerSelect(time) {
      emit("time-picker-select", time);
    }
    function onTodayBtnClick() {
      emit("today-btn-click", getNow());
    }
    function onConfirmBtnClick() {
      emit("confirm");
    }
    function onPanelHeaderLabelClick(type) {
      emit("header-label-click", type);
    }
    function onHeaderPanelSelect(date) {
      emit("header-select", date);
    }
    function onMonthHeaderLabelClick() {
      emit("month-header-click");
    }
    const shortcutsProps = reactive({
      prefixCls,
      shortcuts,
      showNowBtn: showShortcutsNowBtn,
      onItemClick: onShortcutClick,
      onItemMouseEnter: onShortcutMouseEnter,
      onItemMouseLeave: onShortcutMouseLeave,
      onNowClick: onTodayBtnClick
    });
    const commonPanelProps = reactive({
      value,
      headerValue,
      headerIcons,
      headerOperations,
      disabledDate,
      dateRender,
      onSelect: onPanelSelect,
      onHeaderLabelClick: onPanelHeaderLabelClick
    });
    return {
      classNames,
      showShortcutsInLeft,
      showShortcutsInRight,
      showShortcutsInBottom,
      shortcutsProps,
      commonPanelProps,
      footerValue,
      onTodayBtnClick,
      onConfirmBtnClick,
      onTimePickerSelect,
      onHeaderPanelSelect,
      headerPanelHeaderValue,
      headerPanelHeaderOperations,
      onMonthHeaderLabelClick
    };
  }
});
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  const _component_PanelShortcuts = resolveComponent("PanelShortcuts");
  const _component_YearPanel = resolveComponent("YearPanel");
  const _component_MonthPanel = resolveComponent("MonthPanel");
  const _component_WeekPanel = resolveComponent("WeekPanel");
  const _component_QuarterPanel = resolveComponent("QuarterPanel");
  const _component_DatePanel = resolveComponent("DatePanel");
  const _component_RenderFunction = resolveComponent("RenderFunction");
  const _component_PanelFooter = resolveComponent("PanelFooter");
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(_ctx.classNames)
  }, [
    _ctx.showShortcutsInLeft ? (openBlock(), createBlock(_component_PanelShortcuts, normalizeProps(mergeProps({ key: 0 }, _ctx.shortcutsProps)), null, 16)) : createCommentVNode("v-if", true),
    createElementVNode("div", {
      class: normalizeClass(`${_ctx.prefixCls}-panel-wrapper`)
    }, [
      _ctx.headerMode ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
        _ctx.headerMode === "year" ? (openBlock(), createBlock(_component_YearPanel, {
          key: 0,
          "header-value": _ctx.headerPanelHeaderValue,
          "header-icons": _ctx.headerIcons,
          "header-operations": _ctx.headerPanelHeaderOperations,
          onSelect: _ctx.onHeaderPanelSelect
        }, null, 8, ["header-value", "header-icons", "header-operations", "onSelect"])) : _ctx.headerMode === "month" ? (openBlock(), createBlock(_component_MonthPanel, {
          key: 1,
          "header-value": _ctx.headerPanelHeaderValue,
          "header-icons": _ctx.headerIcons,
          "header-operations": _ctx.headerPanelHeaderOperations,
          abbreviation: _ctx.abbreviation,
          onSelect: _ctx.onHeaderPanelSelect,
          onHeaderLabelClick: _ctx.onMonthHeaderLabelClick
        }, null, 8, ["header-value", "header-icons", "header-operations", "abbreviation", "onSelect", "onHeaderLabelClick"])) : createCommentVNode("v-if", true)
      ], 2112)) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
        _ctx.mode === "week" ? (openBlock(), createBlock(_component_WeekPanel, mergeProps({ key: 0 }, _ctx.commonPanelProps, { "day-start-of-week": _ctx.dayStartOfWeek }), null, 16, ["day-start-of-week"])) : _ctx.mode === "month" ? (openBlock(), createBlock(_component_MonthPanel, mergeProps({
          key: 1,
          abbreviation: _ctx.abbreviation
        }, _ctx.commonPanelProps), null, 16, ["abbreviation"])) : _ctx.mode === "year" ? (openBlock(), createBlock(_component_YearPanel, normalizeProps(mergeProps({ key: 2 }, _ctx.commonPanelProps)), null, 16)) : _ctx.mode === "quarter" ? (openBlock(), createBlock(_component_QuarterPanel, normalizeProps(mergeProps({ key: 3 }, _ctx.commonPanelProps)), null, 16)) : (openBlock(), createBlock(_component_DatePanel, mergeProps({ key: 4 }, _ctx.commonPanelProps, {
          mode: "date",
          "show-time": _ctx.showTime,
          "time-picker-props": _ctx.timePickerProps,
          "day-start-of-week": _ctx.dayStartOfWeek,
          "footer-value": _ctx.footerValue,
          "time-picker-value": _ctx.timePickerValue,
          "disabled-time": _ctx.disabledTime,
          onTimePickerSelect: _ctx.onTimePickerSelect
        }), null, 16, ["show-time", "time-picker-props", "day-start-of-week", "footer-value", "time-picker-value", "disabled-time", "onTimePickerSelect"])),
        createVNode(_component_PanelFooter, {
          "prefix-cls": _ctx.prefixCls,
          "show-today-btn": _ctx.showNowBtn && !(_ctx.showConfirmBtn || _ctx.showShortcutsInBottom),
          "show-confirm-btn": _ctx.showConfirmBtn,
          "confirm-btn-disabled": _ctx.confirmBtnDisabled,
          onTodayBtnClick: _ctx.onTodayBtnClick,
          onConfirmBtnClick: _ctx.onConfirmBtnClick
        }, createSlots({ _: 2 }, [
          _ctx.extra ? {
            name: "extra",
            fn: withCtx(() => [
              _ctx.extra ? (openBlock(), createBlock(_component_RenderFunction, {
                key: 0,
                "render-func": _ctx.extra
              }, null, 8, ["render-func"])) : createCommentVNode("v-if", true)
            ])
          } : void 0,
          _ctx.showShortcutsInBottom ? {
            name: "btn",
            fn: withCtx(() => [
              createVNode(_component_PanelShortcuts, normalizeProps(guardReactiveProps(_ctx.shortcutsProps)), null, 16)
            ])
          } : void 0
        ]), 1032, ["prefix-cls", "show-today-btn", "show-confirm-btn", "confirm-btn-disabled", "onTodayBtnClick", "onConfirmBtnClick"])
      ], 64))
    ], 2),
    _ctx.showShortcutsInRight ? (openBlock(), createBlock(_component_PanelShortcuts, normalizeProps(mergeProps({ key: 1 }, _ctx.shortcutsProps)), null, 16)) : createCommentVNode("v-if", true)
  ], 2);
}
var PickerPanel = /* @__PURE__ */ _export_sfc(_sfc_main, [["render", _sfc_render]]);
export { PickerPanel as default };
