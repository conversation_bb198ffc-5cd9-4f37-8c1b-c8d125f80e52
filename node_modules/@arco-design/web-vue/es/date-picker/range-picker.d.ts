import { Dayjs } from 'dayjs';
import { PropType } from 'vue';
import { TimePickerProps } from '../time-picker/interface';
import { DisabledTimeProps, ShortcutType, CalendarValue, WeekStart } from './interface';
import { TriggerProps } from '../trigger';
declare const _default: import("vue").DefineComponent<{
    mode: {
        type: PropType<"year" | "month" | "date" | "week" | "quarter">;
        default: string;
    };
    modelValue: {
        type: PropType<(string | number | Date)[]>;
    };
    defaultValue: {
        type: PropType<(string | number | Date)[]>;
    };
    pickerValue: {
        type: PropType<(string | number | Date)[]>;
    };
    defaultPickerValue: {
        type: PropType<(string | number | Date)[]>;
    };
    disabled: {
        type: PropType<boolean | boolean[]>;
        default: boolean;
    };
    dayStartOfWeek: {
        type: PropType<WeekStart>;
        default: number;
    };
    format: {
        type: StringConstructor;
    };
    valueFormat: {
        type: PropType<string>;
    };
    showTime: {
        type: BooleanConstructor;
    };
    timePickerProps: {
        type: PropType<Partial<TimePickerProps>>;
    };
    placeholder: {
        type: PropType<string[]>;
    };
    disabledDate: {
        type: PropType<(current: Date, type: 'start' | 'end') => boolean>;
    };
    disabledTime: {
        type: PropType<(current: Date, type: 'start' | 'end') => DisabledTimeProps>;
    };
    separator: {
        type: StringConstructor;
    };
    exchangeTime: {
        type: BooleanConstructor;
        default: boolean;
    };
    popupContainer: {
        type: PropType<string | HTMLElement>;
    };
    locale: {
        type: PropType<Record<string, any>>;
    };
    hideTrigger: {
        type: BooleanConstructor;
    };
    allowClear: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
    };
    error: {
        type: BooleanConstructor;
    };
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    shortcuts: {
        type: PropType<ShortcutType[]>;
        default: () => never[];
    };
    shortcutsPosition: {
        type: PropType<"bottom" | "left" | "right">;
        default: string;
    };
    position: {
        type: PropType<"top" | "tl" | "tr" | "bottom" | "bl" | "br">;
        default: string;
    };
    popupVisible: {
        type: BooleanConstructor;
        default: undefined;
    };
    defaultPopupVisible: {
        type: BooleanConstructor;
    };
    triggerProps: {
        type: PropType<TriggerProps>;
    };
    unmountOnClose: {
        type: BooleanConstructor;
    };
    previewShortcut: {
        type: BooleanConstructor;
        default: boolean;
    };
    showConfirmBtn: {
        type: BooleanConstructor;
    };
    disabledInput: {
        type: BooleanConstructor;
        default: boolean;
    };
    abbreviation: {
        type: BooleanConstructor;
        default: boolean;
    };
}, {
    prefixCls: string;
    refInput: import("vue").Ref<any>;
    computedFormat: import("vue").ComputedRef<string>;
    computedPlaceholder: import("vue").ComputedRef<string[]>;
    panelVisible: import("vue").ComputedRef<boolean>;
    panelValue: import("vue").ComputedRef<(Dayjs | undefined)[]>;
    inputValue: import("vue").Ref<(string | undefined)[] | undefined>;
    focusedIndex: import("vue").Ref<number>;
    triggerDisabled: import("vue").ComputedRef<boolean>;
    mergedSize: import("vue").ComputedRef<"mini" | "medium" | "large" | "small" | undefined>;
    mergedError: import("vue").ComputedRef<boolean>;
    onPanelVisibleChange: (visible: boolean) => void;
    onInputClear: (e: Event) => void;
    onInputChange: (e: any) => void;
    onInputPressEnter: () => void;
    rangePanelProps: import("vue").ComputedRef<{
        prefixCls: string;
        format: string;
        value: (Dayjs | undefined)[];
        showConfirmBtn: boolean;
        confirmBtnDisabled: boolean | undefined;
        timePickerValue: Dayjs[];
        timePickerProps: {
            visible: boolean;
            popupVisible?: boolean | undefined;
            defaultPopupVisible?: boolean | undefined;
            position?: "top" | "tl" | "tr" | "bottom" | "bl" | "br" | undefined;
            disabled?: boolean | undefined;
            unmountOnClose?: boolean | undefined;
            size?: "mini" | "medium" | "large" | "small" | undefined;
            type?: "time" | "time-range" | undefined;
            readonly?: boolean | undefined;
            error?: boolean | undefined;
            allowClear?: boolean | undefined;
            placeholder?: string | string[] | undefined;
            format: string;
            modelValue?: string | number | {
                toString: () => string;
                toDateString: () => string;
                toTimeString: () => string;
                toLocaleString: {
                    (): string;
                    (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                };
                toLocaleDateString: {
                    (): string;
                    (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                };
                toLocaleTimeString: {
                    (): string;
                    (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                };
                valueOf: () => number;
                getTime: () => number;
                getFullYear: () => number;
                getUTCFullYear: () => number;
                getMonth: () => number;
                getUTCMonth: () => number;
                getDate: () => number;
                getUTCDate: () => number;
                getDay: () => number;
                getUTCDay: () => number;
                getHours: () => number;
                getUTCHours: () => number;
                getMinutes: () => number;
                getUTCMinutes: () => number;
                getSeconds: () => number;
                getUTCSeconds: () => number;
                getMilliseconds: () => number;
                getUTCMilliseconds: () => number;
                getTimezoneOffset: () => number;
                setTime: (time: number) => number;
                setMilliseconds: (ms: number) => number;
                setUTCMilliseconds: (ms: number) => number;
                setSeconds: (sec: number, ms?: number | undefined) => number;
                setUTCSeconds: (sec: number, ms?: number | undefined) => number;
                setMinutes: (min: number, sec?: number | undefined, ms?: number | undefined) => number;
                setUTCMinutes: (min: number, sec?: number | undefined, ms?: number | undefined) => number;
                setHours: (hours: number, min?: number | undefined, sec?: number | undefined, ms?: number | undefined) => number;
                setUTCHours: (hours: number, min?: number | undefined, sec?: number | undefined, ms?: number | undefined) => number;
                setDate: (date: number) => number;
                setUTCDate: (date: number) => number;
                setMonth: (month: number, date?: number | undefined) => number;
                setUTCMonth: (month: number, date?: number | undefined) => number;
                setFullYear: (year: number, month?: number | undefined, date?: number | undefined) => number;
                setUTCFullYear: (year: number, month?: number | undefined, date?: number | undefined) => number;
                toUTCString: () => string;
                toISOString: () => string;
                toJSON: (key?: any) => string;
                getVarDate: () => VarDate;
                [Symbol.toPrimitive]: {
                    (hint: "default"): string;
                    (hint: "string"): string;
                    (hint: "number"): number;
                    (hint: string): string | number;
                };
            } | (string | number | {
                toString: () => string;
                toDateString: () => string;
                toTimeString: () => string;
                toLocaleString: {
                    (): string;
                    (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                };
                toLocaleDateString: {
                    (): string;
                    (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                };
                toLocaleTimeString: {
                    (): string;
                    (locales?: string | string[] | undefined, options?: Intl.DateTimeFormatOptions | undefined): string;
                };
                valueOf: () => number;
                getTime: () => number;
                getFullYear: () => number;
                getUTCFullYear: () => number;
                getMonth: () => number;
                getUTCMonth: () => number;
                getDate: () => number;
                getUTCDate: () => number;
                getDay: () => number;
                getUTCDay: () => number;
                getHours: () => number;
                getUTCHours: () => number;
                getMinutes: () => number;
                getUTCMinutes: () => number;
                getSeconds: () => number;
                getUTCSeconds: () => number;
                getMilliseconds: () => number;
                getUTCMilliseconds: () => number;
                getTimezoneOffset: () => number;
                setTime: (time: number) => number;
                setMilliseconds: (ms: number) => number;
                setUTCMilliseconds: (ms: number) => number;
                setSeconds: (sec: number, ms?: number | undefined) => number;
                setUTCSeconds: (sec: number, ms?: number | undefined) => number;
                setMinutes: (min: number, sec?: number | undefined, ms?: number | undefined) => number;
                setUTCMinutes: (min: number, sec?: number | undefined, ms?: number | undefined) => number;
                setHours: (hours: number, min?: number | undefined, sec?: number | undefined, ms?: number | undefined) => number;
                setUTCHours: (hours: number, min?: number | undefined, sec?: number | undefined, ms?: number | undefined) => number;
                setDate: (date: number) => number;
                setUTCDate: (date: number) => number;
                setMonth: (month: number, date?: number | undefined) => number;
                setUTCMonth: (month: number, date?: number | undefined) => number;
                setFullYear: (year: number, month?: number | undefined, date?: number | undefined) => number;
                setUTCFullYear: (year: number, month?: number | undefined, date?: number | undefined) => number;
                toUTCString: () => string;
                toISOString: () => string;
                toJSON: (key?: any) => string;
                getVarDate: () => VarDate;
                [Symbol.toPrimitive]: {
                    (hint: "default"): string;
                    (hint: "string"): string;
                    (hint: "number"): number;
                    (hint: string): string | number;
                };
            })[] | undefined;
            triggerProps?: {
                [x: string]: unknown;
            } | undefined;
            disableConfirm?: boolean | undefined;
            use12Hours?: boolean | undefined;
            step?: {
                hour?: number | undefined;
                minute?: number | undefined;
                second?: number | undefined;
            } | undefined;
            disabledHours?: (() => number[]) | undefined;
            disabledMinutes?: ((selectedHour?: number | undefined) => number[]) | undefined;
            disabledSeconds?: ((selectedHour?: number | undefined, selectedMinute?: number | undefined) => number[]) | undefined;
            hideDisabledOptions?: boolean | undefined;
        };
        extra: import("vue").Slot | undefined;
        dateRender: import("vue").Slot | undefined;
        startHeaderProps: {
            headerValue: Dayjs;
            headerOperations: Pick<any, string>;
            headerIcons: {
                prev: import("vue").Slot | undefined;
                prevDouble: import("vue").Slot | undefined;
                next: import("vue").Slot | undefined;
                nextDouble: import("vue").Slot | undefined;
            };
        };
        endHeaderProps: {
            headerValue: Dayjs;
            headerOperations: Pick<any, string>;
            headerIcons: {
                prev: import("vue").Slot | undefined;
                prevDouble: import("vue").Slot | undefined;
                next: import("vue").Slot | undefined;
                nextDouble: import("vue").Slot | undefined;
            };
        };
        footerValue: {
            clone: () => Dayjs;
            isValid: () => boolean;
            year: {
                (): number;
                (value: number): Dayjs;
            };
            month: {
                (): number;
                (value: number): Dayjs;
            };
            date: {
                (): number;
                (value: number): Dayjs;
            };
            day: {
                (): number;
                (value: number): Dayjs;
            };
            hour: {
                (): number;
                (value: number): Dayjs;
            };
            minute: {
                (): number;
                (value: number): Dayjs;
            };
            second: {
                (): number;
                (value: number): Dayjs;
            };
            millisecond: {
                (): number;
                (value: number): Dayjs;
            };
            set: (unit: import("dayjs").UnitType, value: number) => Dayjs;
            get: (unit: import("dayjs").UnitType) => number;
            add: {
                (value: number, unit?: import("dayjs").ManipulateType | undefined): Dayjs;
                (value: number, unit: import("dayjs").QUnitType): Dayjs;
            };
            subtract: {
                (value: number, unit?: import("dayjs").ManipulateType | undefined): Dayjs;
                (value: number, unit: import("dayjs").QUnitType): Dayjs;
            };
            startOf: {
                (unit: import("dayjs").OpUnitType): Dayjs;
                (unit: import("dayjs").QUnitType): Dayjs;
            };
            endOf: {
                (unit: import("dayjs").OpUnitType): Dayjs;
                (unit: import("dayjs").QUnitType): Dayjs;
            };
            format: (template?: string | undefined) => string;
            diff: (date?: string | number | Date | Dayjs | null | undefined, unit?: "M" | "y" | "s" | "year" | "month" | "date" | "day" | "hour" | "minute" | "second" | "millisecond" | "week" | "quarter" | "milliseconds" | "seconds" | "minutes" | "hours" | "days" | "months" | "years" | "dates" | "d" | "h" | "m" | "ms" | "weeks" | "w" | "quarters" | "Q" | undefined, float?: boolean | undefined) => number;
            valueOf: () => number;
            unix: () => number;
            daysInMonth: () => number;
            toDate: () => Date;
            toJSON: () => string;
            toISOString: () => string;
            toString: () => string;
            utcOffset: () => number;
            isBefore: {
                (date: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                (date: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
            };
            isSame: {
                (date: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                (date: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
            };
            isAfter: {
                (date: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").OpUnitType | undefined): boolean;
                (date: string | number | Date | Dayjs | null | undefined, unit?: import("dayjs").QUnitType | undefined): boolean;
            };
            locale: {
                (): string;
                (preset: string | ILocale, object?: Partial<ILocale> | undefined): Dayjs;
            };
            isBetween: (a: string | number | Date | Dayjs | null | undefined, b: string | number | Date | Dayjs | null | undefined, c?: import("dayjs").OpUnitType | null | undefined, d?: "()" | "[]" | "[)" | "(]" | undefined) => boolean;
            week: {
                (): number;
                (value: number): Dayjs;
            };
            weekYear: () => number;
            quarter: {
                (): number;
                (quarter: number): Dayjs;
            };
        }[];
        disabled: boolean[];
        visible: boolean;
        onCellClick: (date: Dayjs) => void;
        onCellMouseEnter: (date: Dayjs) => void;
        onShortcutClick: (value: Array<Dayjs | undefined>, shortcut: ShortcutType) => void;
        onShortcutMouseEnter: ((value: Array<Dayjs | undefined>) => void) | undefined;
        onShortcutMouseLeave: (() => void) | undefined;
        onConfirm: () => void;
        onTimePickerSelect: (time: Dayjs, type: 'start' | 'end') => void;
        startHeaderMode: "year" | "month" | undefined;
        endHeaderMode: "year" | "month" | undefined;
        onStartHeaderLabelClick: (type: 'year' | 'month') => void;
        onEndHeaderLabelClick: (type: 'year' | 'month') => void;
        onStartHeaderSelect: (date: Dayjs) => void;
        onEndHeaderSelect: (date: Dayjs) => void;
        mode: "year" | "month" | "date" | "week" | "quarter";
        hideTrigger: boolean;
        dayStartOfWeek: WeekStart;
        showTime: boolean;
        disabledDate: ((current: Date, type: 'start' | 'end') => boolean) | undefined;
        disabledTime: ((current: Date, type: 'start' | 'end') => DisabledTimeProps) | undefined;
        shortcuts: ShortcutType[];
        abbreviation: boolean;
        shortcutsPosition: "bottom" | "left" | "right";
    }>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    change: (value: (CalendarValue | undefined)[] | undefined, date: (Date | undefined)[] | undefined, dateString: (string | undefined)[] | undefined) => true;
    'update:modelValue': (value: (CalendarValue | undefined)[] | undefined) => true;
    select: (value: (CalendarValue | undefined)[], date: (Date | undefined)[], dateString: (string | undefined)[]) => true;
    'popup-visible-change': (visible: boolean) => true;
    'update:popupVisible': (visible: boolean) => true;
    ok: (value: CalendarValue[], date: Date[], dateString: string[]) => true;
    clear: () => true;
    'select-shortcut': (shortcut: ShortcutType) => true;
    'picker-value-change': (value: CalendarValue[], date: Date[], dateString: string[]) => true;
    'update:pickerValue': (value: CalendarValue[]) => true;
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    mode?: unknown;
    modelValue?: unknown;
    defaultValue?: unknown;
    pickerValue?: unknown;
    defaultPickerValue?: unknown;
    disabled?: unknown;
    dayStartOfWeek?: unknown;
    format?: unknown;
    valueFormat?: unknown;
    showTime?: unknown;
    timePickerProps?: unknown;
    placeholder?: unknown;
    disabledDate?: unknown;
    disabledTime?: unknown;
    separator?: unknown;
    exchangeTime?: unknown;
    popupContainer?: unknown;
    locale?: unknown;
    hideTrigger?: unknown;
    allowClear?: unknown;
    readonly?: unknown;
    error?: unknown;
    size?: unknown;
    shortcuts?: unknown;
    shortcutsPosition?: unknown;
    position?: unknown;
    popupVisible?: unknown;
    defaultPopupVisible?: unknown;
    triggerProps?: unknown;
    unmountOnClose?: unknown;
    previewShortcut?: unknown;
    showConfirmBtn?: unknown;
    disabledInput?: unknown;
    abbreviation?: unknown;
} & {
    defaultPopupVisible: boolean;
    position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
    disabled: boolean | boolean[];
    unmountOnClose: boolean;
    mode: "year" | "month" | "date" | "week" | "quarter";
    readonly: boolean;
    error: boolean;
    allowClear: boolean;
    disabledInput: boolean;
    hideTrigger: boolean;
    dayStartOfWeek: WeekStart;
    showTime: boolean;
    shortcuts: ShortcutType[];
    showConfirmBtn: boolean;
    abbreviation: boolean;
    shortcutsPosition: "bottom" | "left" | "right";
    exchangeTime: boolean;
    previewShortcut: boolean;
} & {
    popupVisible?: boolean | undefined;
    popupContainer?: string | HTMLElement | undefined;
    size?: "mini" | "medium" | "large" | "small" | undefined;
    separator?: string | undefined;
    placeholder?: string[] | undefined;
    format?: string | undefined;
    defaultValue?: (string | number | Date)[] | undefined;
    locale?: Record<string, any> | undefined;
    modelValue?: (string | number | Date)[] | undefined;
    triggerProps?: TriggerProps | undefined;
    timePickerProps?: Partial<TimePickerProps> | undefined;
    disabledDate?: ((current: Date, type: 'start' | 'end') => boolean) | undefined;
    disabledTime?: ((current: Date, type: 'start' | 'end') => DisabledTimeProps) | undefined;
    valueFormat?: string | undefined;
    pickerValue?: (string | number | Date)[] | undefined;
    defaultPickerValue?: (string | number | Date)[] | undefined;
}> & {
    "onUpdate:popupVisible"?: ((visible: boolean) => any) | undefined;
    onChange?: ((value: (CalendarValue | undefined)[] | undefined, date: (Date | undefined)[] | undefined, dateString: (string | undefined)[] | undefined) => any) | undefined;
    onSelect?: ((value: (CalendarValue | undefined)[], date: (Date | undefined)[], dateString: (string | undefined)[]) => any) | undefined;
    onClear?: (() => any) | undefined;
    "onUpdate:modelValue"?: ((value: (CalendarValue | undefined)[] | undefined) => any) | undefined;
    "onPopup-visible-change"?: ((visible: boolean) => any) | undefined;
    onOk?: ((value: CalendarValue[], date: Date[], dateString: string[]) => any) | undefined;
    "onSelect-shortcut"?: ((shortcut: ShortcutType) => any) | undefined;
    "onPicker-value-change"?: ((value: CalendarValue[], date: Date[], dateString: string[]) => any) | undefined;
    "onUpdate:pickerValue"?: ((value: CalendarValue[]) => any) | undefined;
}, {
    popupVisible: boolean;
    defaultPopupVisible: boolean;
    position: "top" | "tl" | "tr" | "bottom" | "bl" | "br";
    disabled: boolean | boolean[];
    unmountOnClose: boolean;
    mode: "year" | "month" | "date" | "week" | "quarter";
    readonly: boolean;
    error: boolean;
    allowClear: boolean;
    disabledInput: boolean;
    hideTrigger: boolean;
    dayStartOfWeek: WeekStart;
    showTime: boolean;
    shortcuts: ShortcutType[];
    showConfirmBtn: boolean;
    abbreviation: boolean;
    shortcutsPosition: "bottom" | "left" | "right";
    exchangeTime: boolean;
    previewShortcut: boolean;
}>;
export default _default;
