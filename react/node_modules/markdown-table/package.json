{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/wooorm/markdown-table/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "description": "Generate a markdown (GFM) table", "devDependencies": {"@types/node": "^22.0.0", "c8": "^10.0.0", "chalk": "^5.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^10.0.0", "strip-ansi": "^7.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.59.0"}, "files": ["index.d.ts.map", "index.d.ts", "index.js"], "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "keywords": ["align", "markdown", "rows", "table", "tabular", "text"], "license": "MIT", "main": "index.js", "name": "markdown-table", "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "repository": "wooorm/markdown-table", "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark --frail --output --quiet -- . && prettier --log-level warn --write -- . && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --100 --check-coverage --reporter lcov -- npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true}, "types": "index.d.ts", "type": "module", "version": "3.0.4", "xo": {"prettier": true, "rules": {"complexity": "off", "unicorn/prefer-switch": "off"}}}