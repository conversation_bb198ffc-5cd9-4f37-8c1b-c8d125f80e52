<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8">
    <style>
        body, html, div{
            width: 100%;
            height: 100%;
            margin: 0;
        }
        div{
            width: 1024px;
            height: 768px;
            border: 5px solid green;
            background-color: red;
            margin: 0;
        }

    </style>



</head>
    <body>
        <div id="pusher"></div>
        <script>
            var ZERO_WIDTH_JOINER_CODE = 8205; // U+200D
            var VARIATION_SELECTOR_16 = 65039; // U+FE0F
            // var text= '🏳️‍🌈';
            var text= '🏴‍☠️';
            // var text= '👨🏽‍🌾';
            // var text= '👩‍👩‍👧‍👧';
            // var text= '👨‍🦱';
            // var text= '👨🏿';

            for (var i = 0; i < text.length; i++) {
                console.log('INDEX: ', i);
                var charCode = text.charCodeAt(i);
                console.log('CHAR CODE: ', charCode);
                if (charCode >= 0xD800 && charCode <= 0xDBFF) {
                    console.log('HIGH SURROGATE');
                }
                if (charCode >= 0xDC00 && charCode <= 0xDFFF) {
                    console.log('LOW SURROGATE');
                }
                if (charCode === ZERO_WIDTH_JOINER_CODE) {
                    console.log('ZERO WIDTH JOINENR')
                }
                if (charCode === VARIATION_SELECTOR_16) {
                    console.log('VARIATION SELECTOR')
                }
                console.log('=====================');
            }
        </script>
    </body>
</html>
