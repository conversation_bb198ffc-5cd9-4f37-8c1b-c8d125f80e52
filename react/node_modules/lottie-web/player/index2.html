<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8">
    <style>
        html, body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        .container {
            width: 100%;
            height: 100%;
            background-color: #ff0000;
            display: grid;
            grid-template-columns: 100%;
            grid-template-rows: 50px auto 50px;
        }
        .header {
            grid-column: 1;
            grid-row: 1;
            background-color: #00ff00;
        }
        .main {
            grid-column: 1;
            grid-row: 2;
            background-color: #0000ff;
            min-height: 0;
            min-width: 0;
        }
        .main-content {
            width: 100%;
            height: 800px;
            padding: 20px;
            background-color: #000000;
        }
        .footer {
            grid-column: 1;
            grid-row: 3;
            background-color: #ffff00;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">Header</div>
    <div class="main">
        <div class="main-content">

        </div>
    </div>
    <div class="footer">Footer</div>
</div>

</body>
</html>
